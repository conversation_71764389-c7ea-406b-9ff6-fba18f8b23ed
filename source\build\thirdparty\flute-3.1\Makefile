# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/cmake/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/cmake/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/opt/cmake/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/opt/cmake/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_progress_start /public/home/<USER>/DREAMPlace/source/build/CMakeFiles /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1/CMakeFiles/progress.marks
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/all
	$(CMAKE_COMMAND) -E cmake_progress_start /public/home/<USER>/DREAMPlace/source/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
thirdparty/flute-3.1/CMakeFiles/flute.dir/rule:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/CMakeFiles/flute.dir/rule
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute.dir/rule

# Convenience name for target.
flute: thirdparty/flute-3.1/CMakeFiles/flute.dir/rule

.PHONY : flute

# fast build rule for target.
flute/fast:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/build
.PHONY : flute/fast

# Convenience name for target.
thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rule:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rule
.PHONY : thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rule

# Convenience name for target.
rand-pts: thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rule

.PHONY : rand-pts

# fast build rule for target.
rand-pts/fast:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build.make thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build
.PHONY : rand-pts/fast

# Convenience name for target.
thirdparty/flute-3.1/CMakeFiles/flute-net.dir/rule:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/CMakeFiles/flute-net.dir/rule
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute-net.dir/rule

# Convenience name for target.
flute-net: thirdparty/flute-3.1/CMakeFiles/flute-net.dir/rule

.PHONY : flute-net

# fast build rule for target.
flute-net/fast:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build
.PHONY : flute-net/fast

# Convenience name for target.
thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/rule:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f CMakeFiles/Makefile2 thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/rule
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/rule

# Convenience name for target.
flute-ckt: thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/rule

.PHONY : flute-ckt

# fast build rule for target.
flute-ckt/fast:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build
.PHONY : flute-ckt/fast

bookshelf_IO.o: bookshelf_IO.c.o

.PHONY : bookshelf_IO.o

# target to build an object file
bookshelf_IO.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.o
.PHONY : bookshelf_IO.c.o

bookshelf_IO.i: bookshelf_IO.c.i

.PHONY : bookshelf_IO.i

# target to preprocess a source file
bookshelf_IO.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.i
.PHONY : bookshelf_IO.c.i

bookshelf_IO.s: bookshelf_IO.c.s

.PHONY : bookshelf_IO.s

# target to generate assembly for a file
bookshelf_IO.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.s
.PHONY : bookshelf_IO.c.s

dist.o: dist.c.o

.PHONY : dist.o

# target to build an object file
dist.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.o
.PHONY : dist.c.o

dist.i: dist.c.i

.PHONY : dist.i

# target to preprocess a source file
dist.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.i
.PHONY : dist.c.i

dist.s: dist.c.s

.PHONY : dist.s

# target to generate assembly for a file
dist.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.s
.PHONY : dist.c.s

dl.o: dl.c.o

.PHONY : dl.o

# target to build an object file
dl.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.o
.PHONY : dl.c.o

dl.i: dl.c.i

.PHONY : dl.i

# target to preprocess a source file
dl.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.i
.PHONY : dl.c.i

dl.s: dl.c.s

.PHONY : dl.s

# target to generate assembly for a file
dl.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.s
.PHONY : dl.c.s

err.o: err.c.o

.PHONY : err.o

# target to build an object file
err.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.o
.PHONY : err.c.o

err.i: err.c.i

.PHONY : err.i

# target to preprocess a source file
err.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.i
.PHONY : err.c.i

err.s: err.c.s

.PHONY : err.s

# target to generate assembly for a file
err.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.s
.PHONY : err.c.s

flute-ckt.o: flute-ckt.c.o

.PHONY : flute-ckt.o

# target to build an object file
flute-ckt.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/flute-ckt.c.o
.PHONY : flute-ckt.c.o

flute-ckt.i: flute-ckt.c.i

.PHONY : flute-ckt.i

# target to preprocess a source file
flute-ckt.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/flute-ckt.c.i
.PHONY : flute-ckt.c.i

flute-ckt.s: flute-ckt.c.s

.PHONY : flute-ckt.s

# target to generate assembly for a file
flute-ckt.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/flute-ckt.c.s
.PHONY : flute-ckt.c.s

flute-net.o: flute-net.c.o

.PHONY : flute-net.o

# target to build an object file
flute-net.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.o
.PHONY : flute-net.c.o

flute-net.i: flute-net.c.i

.PHONY : flute-net.i

# target to preprocess a source file
flute-net.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.i
.PHONY : flute-net.c.i

flute-net.s: flute-net.c.s

.PHONY : flute-net.s

# target to generate assembly for a file
flute-net.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.s
.PHONY : flute-net.c.s

flute.o: flute.c.o

.PHONY : flute.o

# target to build an object file
flute.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.o
.PHONY : flute.c.o

flute.i: flute.c.i

.PHONY : flute.i

# target to preprocess a source file
flute.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.i
.PHONY : flute.c.i

flute.s: flute.c.s

.PHONY : flute.s

# target to generate assembly for a file
flute.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.s
.PHONY : flute.c.s

flute_mst.o: flute_mst.c.o

.PHONY : flute_mst.o

# target to build an object file
flute_mst.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.o
.PHONY : flute_mst.c.o

flute_mst.i: flute_mst.c.i

.PHONY : flute_mst.i

# target to preprocess a source file
flute_mst.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.i
.PHONY : flute_mst.c.i

flute_mst.s: flute_mst.c.s

.PHONY : flute_mst.s

# target to generate assembly for a file
flute_mst.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.s
.PHONY : flute_mst.c.s

heap.o: heap.c.o

.PHONY : heap.o

# target to build an object file
heap.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.o
.PHONY : heap.c.o

heap.i: heap.c.i

.PHONY : heap.i

# target to preprocess a source file
heap.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.i
.PHONY : heap.c.i

heap.s: heap.c.s

.PHONY : heap.s

# target to generate assembly for a file
heap.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.s
.PHONY : heap.c.s

memAlloc.o: memAlloc.c.o

.PHONY : memAlloc.o

# target to build an object file
memAlloc.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.o
.PHONY : memAlloc.c.o

memAlloc.i: memAlloc.c.i

.PHONY : memAlloc.i

# target to preprocess a source file
memAlloc.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.i
.PHONY : memAlloc.c.i

memAlloc.s: memAlloc.c.s

.PHONY : memAlloc.s

# target to generate assembly for a file
memAlloc.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.s
.PHONY : memAlloc.c.s

mst2.o: mst2.c.o

.PHONY : mst2.o

# target to build an object file
mst2.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.o
.PHONY : mst2.c.o

mst2.i: mst2.c.i

.PHONY : mst2.i

# target to preprocess a source file
mst2.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.i
.PHONY : mst2.c.i

mst2.s: mst2.c.s

.PHONY : mst2.s

# target to generate assembly for a file
mst2.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.s
.PHONY : mst2.c.s

neighbors.o: neighbors.c.o

.PHONY : neighbors.o

# target to build an object file
neighbors.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.o
.PHONY : neighbors.c.o

neighbors.i: neighbors.c.i

.PHONY : neighbors.i

# target to preprocess a source file
neighbors.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.i
.PHONY : neighbors.c.i

neighbors.s: neighbors.c.s

.PHONY : neighbors.s

# target to generate assembly for a file
neighbors.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.s
.PHONY : neighbors.c.s

rand-pts.o: rand-pts.c.o

.PHONY : rand-pts.o

# target to build an object file
rand-pts.c.o:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build.make thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.o
.PHONY : rand-pts.c.o

rand-pts.i: rand-pts.c.i

.PHONY : rand-pts.i

# target to preprocess a source file
rand-pts.c.i:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build.make thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.i
.PHONY : rand-pts.c.i

rand-pts.s: rand-pts.c.s

.PHONY : rand-pts.s

# target to generate assembly for a file
rand-pts.c.s:
	cd /public/home/<USER>/DREAMPlace/source/build && $(MAKE) -f thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build.make thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.s
.PHONY : rand-pts.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... flute"
	@echo "... rand-pts"
	@echo "... flute-net"
	@echo "... flute-ckt"
	@echo "... bookshelf_IO.o"
	@echo "... bookshelf_IO.i"
	@echo "... bookshelf_IO.s"
	@echo "... dist.o"
	@echo "... dist.i"
	@echo "... dist.s"
	@echo "... dl.o"
	@echo "... dl.i"
	@echo "... dl.s"
	@echo "... err.o"
	@echo "... err.i"
	@echo "... err.s"
	@echo "... flute-ckt.o"
	@echo "... flute-ckt.i"
	@echo "... flute-ckt.s"
	@echo "... flute-net.o"
	@echo "... flute-net.i"
	@echo "... flute-net.s"
	@echo "... flute.o"
	@echo "... flute.i"
	@echo "... flute.s"
	@echo "... flute_mst.o"
	@echo "... flute_mst.i"
	@echo "... flute_mst.s"
	@echo "... heap.o"
	@echo "... heap.i"
	@echo "... heap.s"
	@echo "... memAlloc.o"
	@echo "... memAlloc.i"
	@echo "... memAlloc.s"
	@echo "... mst2.o"
	@echo "... mst2.i"
	@echo "... mst2.s"
	@echo "... neighbors.o"
	@echo "... neighbors.i"
	@echo "... neighbors.s"
	@echo "... rand-pts.o"
	@echo "... rand-pts.i"
	@echo "... rand-pts.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


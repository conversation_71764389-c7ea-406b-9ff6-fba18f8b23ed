/**
 * @file   hpwl_hip_atomic.cpp
 * <AUTHOR>
 * @date   10 2024
 * @brief  Compute half-perimeter wirelength to mimic a parallel atomic implementation
 */
#include "utility/src/torch.h"
#include "utility/src/Msg.h"

DREAMPLACE_BEGIN_NAMESPACE

template <typename T>
int computeHPWLHipAtomicLauncher(
        const T* x, const T* y,
        const int* pin2net_map,
        const unsigned char* net_mask,
        int num_nets,
        int num_pins,
        T* partial_hpwl_max,
        T* partial_hpwl_min
        );

#define CHECK_FLAT(x) AT_ASSERTM(x.is_cuda() && x.ndimension() == 1, #x "must be a flat tensor on GPU")
#define CHECK_EVEN(x) AT_ASSERTM((x.numel()&1) == 0, #x "must have even number of elements")
#define CHECK_CONTIGUOUS(x) AT_ASSERTM(x.is_contiguous(), #x "must be contiguous")

/// @brief Compute half-perimeter wirelength
/// @param pos cell locations, array of x locations and then y locations
/// @param pin2net_map map pin to net
/// @param net_mask an array to record whether compute the where for a net or not
at::Tensor hpwl_atomic_forward(
        at::Tensor pos,
        at::Tensor pin2net_map,
        at::Tensor net_mask)
{
    typedef int T;

    CHECK_FLAT(pos);
    CHECK_EVEN(pos);
    CHECK_CONTIGUOUS(pos);
    CHECK_FLAT(pin2net_map);
    CHECK_CONTIGUOUS(pin2net_map);

    int num_nets = net_mask.numel();
    // x then y
    at::Tensor scaled_pos = at::_cast_Int(pos.mul(1000), false);
    at::Tensor partial_hpwl_max = at::zeros({2, num_nets}, scaled_pos.type());
    at::Tensor partial_hpwl_min = at::zeros({2, num_nets}, scaled_pos.type());
    partial_hpwl_max[0].masked_fill_(net_mask, std::numeric_limits<T>::min());
    partial_hpwl_max[1].masked_fill_(net_mask, std::numeric_limits<T>::min());
    partial_hpwl_min[0].masked_fill_(net_mask, std::numeric_limits<T>::max());
    partial_hpwl_min[1].masked_fill_(net_mask, std::numeric_limits<T>::max());

    computeHPWLHipAtomicLauncher<T>(
            scaled_pos.data<T>(), scaled_pos.data<T>()+scaled_pos.numel()/2,
            pin2net_map.data<int>(),
            net_mask.data<unsigned char>(),
            num_nets,
            pin2net_map.numel(),
            partial_hpwl_max.data<T>(),
            partial_hpwl_min.data<T>()
            );

    //std::cout << "partial_hpwl_max = " << partial_hpwl_max << "\n";
    //std::cout << "partial_hpwl_min = " << partial_hpwl_min << "\n";
    //std::cout << "partial_hpwl = \n" << (partial_hpwl_max-partial_hpwl_min)._cast_double().mul(1.0/1000) << "\n";

    auto hpwl = at::_cast_Long(partial_hpwl_max-partial_hpwl_min, false).sum();

    //const at::Type& the_type = pos.type();
    switch (pos.scalar_type())
    {
        case at::ScalarType::Double:
            return at::_cast_Double(hpwl).mul(1.0/1000);
        case at::ScalarType::Float:
            return at::_cast_Float(hpwl).mul(1.0/1000);
        default:
            AT_ERROR("hpwl_atomic_forward", " not implemented for '", at::toString(pos.scalar_type()), "'");
    }

    return hpwl;
}

DREAMPLACE_END_NAMESPACE

PYBIND11_MODULE(TORCH_EXTENSION_NAME, m) {
  m.def("forward", &DREAMPLACE_NAMESPACE::hpwl_atomic_forward, "HPWL forward (HIP)");
}

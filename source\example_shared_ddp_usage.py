#!/usr/bin/env python3
##
# @file   example_shared_ddp_usage.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Example usage of shared parameter DDP placement
#

import os
import sys
import json
import torch
import torch.multiprocessing as mp

# Add dreamplace to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dreamplace'))

from dreamplace.ddp_shared_param_utils import setup_ddp, cleanup_ddp, get_ddp_info
import Params
import PlaceDB
import NonLinearPlace_shared_ddp

def example_ddp_worker(rank, world_size, config_file):
    """
    @brief Example DDP worker function
    @param rank current process rank
    @param world_size total number of processes
    @param config_file path to configuration file
    """
    try:
        print(f"[Rank {rank}] Starting shared parameter DDP placement")
        
        # Setup DDP
        if world_size > 1:
            setup_ddp(rank, world_size)
        
        # Load parameters
        params = Params.Params()
        params.load(config_file)
        
        # Set random seed for reproducibility
        torch.manual_seed(params.random_seed + rank)
        
        # Read database
        placedb = PlaceDB.PlaceDB()
        placedb(params)
        
        if rank == 0:
            print(f"[I] Loaded design: {params.design_name()}")
            print(f"[I] Nodes: {placedb.num_nodes}, Nets: {len(placedb.net2pin_map)}")
        
        # Create shared parameter DDP placer
        placer = NonLinearPlace_shared_ddp.NonLinearPlaceSharedDDP(
            params, placedb, rank, world_size
        )
        
        # Run placement
        metrics = placer(params, placedb)
        
        if rank == 0:
            print(f"[I] Placement completed with {len(metrics)} iterations")
            if metrics:
                final_metric = metrics[-1]
                print(f"[I] Final HPWL: {final_metric.hpwl}")
                print(f"[I] Final overflow: {final_metric.overflow}")
        
    except Exception as e:
        print(f"[Rank {rank}] Error: {e}")
        raise e
    finally:
        # Cleanup DDP
        if world_size > 1:
            cleanup_ddp()

def create_example_config(aux_file, output_dir):
    """Create an example configuration for testing"""
    config = {
        "aux_file": aux_file,
        "gpu": True,
        "num_threads": 8,
        "global_place_flag": True,
        "legalize_flag": True,
        "detailed_place_flag": False,
        "stop_overflow": 0.1,
        "density_weight": 8e-5,
        "target_density": 1.0,
        "random_seed": 1000,
        "scale_factor": 1.0,
        "ignore_net_degree": 100,
        "gp_noise_ratio": 0.025,
        "enable_fillers": True,
        "plot_flag": False,
        "result_dir": output_dir,
        "global_place_stages": [
            {
                "num_bins_x": 32,
                "num_bins_y": 32,
                "iteration": 100,  # Reduced for testing
                "learning_rate": 0.01,
                "wirelength": "weighted_average",
                "optimizer": "nesterov"
            }
        ],
        "RePlAce_ref_hpwl": 350000000,
        "RePlAce_LOWER_PCOF": 0.95,
        "RePlAce_UPPER_PCOF": 1.05
    }
    return config

def run_shared_ddp_example(aux_file, output_dir="example_output", num_gpus=None):
    """
    @brief Run shared parameter DDP placement example
    @param aux_file path to .aux file
    @param output_dir output directory
    @param num_gpus number of GPUs to use (None for all available)
    """
    # Check if aux file exists
    if not os.path.exists(aux_file):
        print(f"[E] AUX file not found: {aux_file}")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Create configuration
    config = create_example_config(os.path.abspath(aux_file), output_dir)
    config_file = os.path.join(output_dir, 'example_config.json')
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"[I] Configuration saved to: {config_file}")
    
    # Determine number of GPUs
    if torch.cuda.is_available():
        available_gpus = torch.cuda.device_count()
        world_size = num_gpus if num_gpus is not None else available_gpus
        world_size = min(world_size, available_gpus)
        
        if world_size > 1:
            print(f"[I] Running shared parameter DDP with {world_size} GPUs")
            try:
                mp.spawn(example_ddp_worker, args=(world_size, config_file), 
                        nprocs=world_size, join=True)
                print("[I] Shared parameter DDP placement completed successfully")
                return True
            except Exception as e:
                print(f"[E] DDP placement failed: {e}")
                print("[I] Falling back to single GPU")
                world_size = 1
        
        if world_size == 1:
            print("[I] Running single GPU placement")
            example_ddp_worker(0, 1, config_file)
            return True
    else:
        print("[I] CUDA not available, running CPU placement")
        config['gpu'] = False
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        example_ddp_worker(0, 1, config_file)
        return True

def main():
    """Main function for example usage"""
    if len(sys.argv) < 2:
        print("Usage: python example_shared_ddp_usage.py <aux_file> [output_dir] [num_gpus]")
        print("Example: python example_shared_ddp_usage.py design.aux output 4")
        return
    
    aux_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else "example_output"
    num_gpus = int(sys.argv[3]) if len(sys.argv) > 3 else None
    
    print("=" * 60)
    print("Shared Parameter DDP Placement Example")
    print("=" * 60)
    print(f"AUX file: {aux_file}")
    print(f"Output directory: {output_dir}")
    print(f"Number of GPUs: {num_gpus if num_gpus else 'all available'}")
    print("=" * 60)
    
    success = run_shared_ddp_example(aux_file, output_dir, num_gpus)
    
    if success:
        print("=" * 60)
        print("Example completed successfully!")
        print("=" * 60)
    else:
        print("=" * 60)
        print("Example failed!")
        print("=" * 60)
        sys.exit(1)

if __name__ == '__main__':
    main()

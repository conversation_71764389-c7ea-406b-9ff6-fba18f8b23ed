# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for rmst_wl.

# Include the progress variables for this target.
include dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/progress.make

dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl: dreamplace/ops/rmst_wl/rmst_wl.stamp


dreamplace/ops/rmst_wl/rmst_wl.stamp: ../dreamplace/ops/rmst_wl/src/rmst_wl.cpp
dreamplace/ops/rmst_wl/rmst_wl.stamp: thirdparty/flute-3.1/libflute.a
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating rmst_wl.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl/rmst_wl.stamp

rmst_wl: dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl
rmst_wl: dreamplace/ops/rmst_wl/rmst_wl.stamp
rmst_wl: dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/build.make

.PHONY : rmst_wl

# Rule to build all files generated by this target.
dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/build: rmst_wl

.PHONY : dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/build

dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl && $(CMAKE_COMMAND) -P CMakeFiles/rmst_wl.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/clean

dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/rmst_wl /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/depend


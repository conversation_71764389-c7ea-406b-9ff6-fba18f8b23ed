# DreamPlace DDP 广播超时问题修复

## 问题描述

在运行 DreamPlace 共享参数 DDP 训练时，遇到以下错误：

```
RuntimeError: [Rank 1] Caught collective operation timeout: WorkNCCL(SeqNum=7, OpType=BROADCAST, TensorShape=[1], Timeout(ms)=1800000) ran for 1800001 milliseconds before timing out.
```

这个错误发生在 `NonLinearPlace_shared_ddp.py` 第168行的 `dist.broadcast(model.density_weight, src=0)` 操作。

## 根本原因

1. **NCCL 通信超时**：默认的 NCCL 超时设置（30分钟）在某些情况下不够
2. **进程同步问题**：不同 GPU 进程可能在不同时间到达广播点
3. **网络或硬件延迟**：GPU 间通信可能因硬件问题而延迟
4. **缺乏错误恢复机制**：一旦超时就会导致所有进程死锁

## 修复方案

### 1. 增强的广播函数 (`ddp_shared_param_utils.py`)

添加了 `broadcast_tensor_with_timeout()` 函数，具有以下特性：

- **超时处理**：可配置的超时时间
- **线程安全**：使用独立线程执行广播操作
- **错误恢复**：超时时不会导致进程崩溃
- **日志记录**：详细的错误和调试信息

### 2. 改进的训练循环 (`NonLinearPlace_shared_ddp.py`)

修改了两个关键的广播操作：

#### 密度权重和 gamma 广播（第170-186行）：
```python
# Broadcast updated weights to all ranks with timeout handling
if self.ddp_world_size > 1:
    try:
        # Add synchronization before broadcast to ensure all ranks are ready
        dist.barrier()
        
        # Broadcast with explicit timeout handling
        from dreamplace.ddp_shared_param_utils import broadcast_tensor_with_timeout
        broadcast_tensor_with_timeout(model.density_weight, src=0, timeout_seconds=300)
        broadcast_tensor_with_timeout(model.gamma, src=0, timeout_seconds=300)
        
    except Exception as broadcast_error:
        print(f"[E] Rank {self.ddp_rank}: Broadcast failed: {broadcast_error}")
        # Fallback: continue with local values to avoid deadlock
        if self.ddp_rank != 0:
            print(f"[W] Rank {self.ddp_rank}: Using local density_weight and gamma values")
        # Don't raise exception to avoid stopping all processes
```

#### 停止决策广播（第205-218行）：
```python
# Broadcast stopping decision to all ranks with timeout handling
if self.ddp_world_size > 1:
    try:
        stop_tensor = torch.tensor([should_stop], dtype=torch.bool, device=self.device)
        from dreamplace.ddp_shared_param_utils import broadcast_tensor_with_timeout
        broadcast_tensor_with_timeout(stop_tensor, src=0, timeout_seconds=60)
        should_stop = stop_tensor.item()
    except Exception as stop_broadcast_error:
        print(f"[E] Rank {self.ddp_rank}: Stop decision broadcast failed: {stop_broadcast_error}")
        # Fallback: non-rank-0 processes continue, rank-0 decides
        if self.ddp_rank != 0:
            should_stop = False  # Continue training on non-rank-0 processes
            print(f"[W] Rank {self.ddp_rank}: Using local stop decision (continue)")
        # Rank 0 keeps its original decision
```

### 3. 增强的 NCCL 配置

改进了 NCCL 环境变量设置：

```python
# Deadlock prevention settings - more conservative timeouts
os.environ.setdefault('NCCL_TIMEOUT', '1800')  # 30 minutes timeout (increased from 10)
os.environ.setdefault('NCCL_BLOCKING_WAIT', '1')  # Better error reporting
os.environ.setdefault('NCCL_ASYNC_ERROR_HANDLING', '1')  # Async error handling
os.environ.setdefault('NCCL_DEBUG', 'WARN')  # Enable warnings

# Additional stability settings
os.environ.setdefault('NCCL_IB_DISABLE', '1')  # Disable InfiniBand if causing issues
os.environ.setdefault('NCCL_P2P_DISABLE', '1')  # Disable P2P if causing issues
os.environ.setdefault('NCCL_SHM_DISABLE', '1')  # Disable shared memory if causing issues
```

## 使用方法

### 1. 测试修复

运行测试脚本验证修复是否有效：

```bash
python test_broadcast_timeout_fix.py
```

### 2. 运行训练

正常运行 DreamPlace DDP 训练：

```bash
python example_shared_ddp_usage.py
```

### 3. 监控日志

注意观察以下日志信息：

- `[E] Rank X: Broadcast failed:` - 广播失败
- `[W] Rank X: Using local density_weight and gamma values` - 使用本地值的回退
- `[W] Rank X: Using local stop decision (continue)` - 使用本地停止决策

## 预期效果

1. **减少超时**：更长的超时时间和更好的同步
2. **优雅降级**：即使广播失败也不会导致训练停止
3. **更好的错误报告**：详细的日志帮助诊断问题
4. **提高稳定性**：避免因单次通信失败导致的训练中断

## 注意事项

1. **性能影响**：添加的 `barrier()` 可能略微影响性能
2. **一致性**：在广播失败时，不同 GPU 可能使用不同的参数值
3. **调试**：启用 NCCL 调试可能产生大量日志输出

## 故障排除

如果仍然遇到超时问题：

1. **增加超时时间**：修改 `timeout_seconds` 参数
2. **检查网络**：确保 GPU 间网络连接稳定
3. **减少并发**：尝试使用更少的 GPU
4. **硬件检查**：检查 GPU 和网络硬件状态

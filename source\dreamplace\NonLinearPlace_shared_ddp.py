##
# @file   NonLinearPlace_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware nonlinear placement engine
#

import os
import sys
import time
import pickle
import numpy as np
import torch
import torch.distributed as dist
import gzip
if sys.version_info[0] < 3:
    import cPickle as pickle
else:
    import _pickle as pickle

import BasicPlace_shared_ddp
import PlaceObj_shared_ddp
import ConjugateGradientOptimizer
import NesterovAcceleratedGradientOptimizer
import LineSearch
import EvalMetrics
from dreamplace.ddp_shared_param_utils import setup_ddp, cleanup_ddp
import pdb

class NonLinearPlaceSharedDDP(BasicPlace_shared_ddp.BasicPlaceSharedDDP):
    """
    @brief Shared parameter DDP-aware nonlinear placement engine
    Node positions are shared parameters across all GPUs
    """
    def __init__(self, params, placedb, ddp_rank=0, ddp_world_size=1):
        """
        @brief initialization for shared parameter DDP placement
        @param params parameters
        @param placedb placement database
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        self.ddp_rank = ddp_rank
        self.ddp_world_size = ddp_world_size
        
        # Initialize DDP if not already done
        if ddp_world_size > 1 and not dist.is_initialized():
            try:
                setup_ddp(ddp_rank, ddp_world_size)
            except Exception as e:
                print(f"[E] Failed to initialize DDP: {e}")
                raise e
        
        # Set device for current rank
        if params.gpu and ddp_world_size > 1:
            torch.cuda.set_device(ddp_rank)
            self.device = torch.device(f"cuda:{ddp_rank}")
        else:
            self.device = torch.device("cuda:0" if params.gpu else "cpu")
        
        # Initialize base class (already handles DDP setup)
        super(NonLinearPlaceSharedDDP, self).__init__(params, placedb, ddp_rank, ddp_world_size)
        
        print(f"[I] GPU {ddp_rank}: Shared parameter DDP NonLinearPlace initialized")

    def __call__(self, params, placedb):
        """
        @brief Top API to solve placement with shared parameter DDP
        @param params parameters
        @param placedb placement database
        """
        iteration = 0
        metrics = []

        # global placement
        if params.global_place_flag:
            for global_place_params in params.global_place_stages:
                if params.gpu:
                    torch.cuda.synchronize()
                tt = time.time()
                
                # construct model and optimizer
                density_weight = metrics[-1].density_weight if metrics else 0.0
                
                # Create shared parameter DDP-aware placement model
                model = PlaceObj_shared_ddp.PlaceObjSharedDDP(
                    density_weight, params, placedb, 
                    self.data_collections, self.op_collections, 
                    global_place_params, self.ddp_rank, self.ddp_world_size
                ).to(self.device)
                
                optimizer_name = global_place_params["optimizer"]

                # determine optimizer (same as original)
                if optimizer_name.lower() == "adam":
                    optimizer = torch.optim.Adam(self.parameters(), lr=model.learning_rate)
                elif optimizer_name.lower() == "sgd":
                    optimizer = torch.optim.SGD(self.parameters(), lr=model.learning_rate)
                elif optimizer_name.lower() == "sgd_momentum":
                    optimizer = torch.optim.SGD(self.parameters(), lr=model.learning_rate, momentum=0.9, nesterov=False)
                elif optimizer_name.lower() == "sgd_nesterov":
                    optimizer = torch.optim.SGD(self.parameters(), lr=model.learning_rate, momentum=0.9, nesterov=True)
                elif optimizer_name.lower() == "cg":
                    optimizer = ConjugateGradientOptimizer.ConjugateGradientOptimizer(self.parameters(), lr=model.learning_rate)
                elif optimizer_name.lower() == "cgls":
                    optimizer = ConjugateGradientOptimizer.ConjugateGradientOptimizer(self.parameters(), lr=model.learning_rate, line_search_fn=LineSearch.build_line_search_fn_armijo(model.obj_fn))
                elif optimizer_name.lower() == "nesterov":
                    optimizer = NesterovAcceleratedGradientOptimizer.NesterovAcceleratedGradientOptimizer(self.parameters(), lr=model.learning_rate,
                            obj_and_grad_fn=model.obj_and_grad_fn,
                            constraint_fn=self.op_collections.move_boundary_op,
                            )
                else:
                    assert 0, "unknown optimizer %s" % (optimizer_name)

                if self.ddp_rank == 0:
                    print("[I] use %s optimizer" % (optimizer_name))
                    
                model.train()
                learning_rate = model.learning_rate
                
                # defining evaluation ops (only on rank 0 for efficiency)
                eval_ops = {}
                if self.ddp_rank == 0:
                    eval_ops = {
                        "hpwl" : self.op_collections.hpwl_op,
                        "overflow" : self.op_collections.density_overflow_op
                    }

                if iteration == 0:
                    if params.gp_noise_ratio > 0.0:
                        if self.ddp_rank == 0:
                            print("[I] add %g%% noise" % (params.gp_noise_ratio*100))
                        model.op_collections.noise_op(model.data_collections.pos[0], params.gp_noise_ratio)

                if params.gpu:
                    torch.cuda.synchronize()
                if self.ddp_rank == 0:
                    print("[I] %s initialization takes %g seconds" % (optimizer_name, (time.time()-tt)))

                for step in range(model.iteration):
                    # metric for this iteration (only on rank 0)
                    cur_metric = None
                    if self.ddp_rank == 0:
                        cur_metric = EvalMetrics.EvalMetrics(iteration)
                        metrics.append(cur_metric)

                    t0 = time.time()

                    # move any out-of-bound cell back to placement region
                    # For shared parameter DDP, this operates on shared positions
                    self.op_collections.move_boundary_op(model.data_collections.pos[0])

                    if torch.eq(model.density_weight, 0.0):
                        model.initialize_density_weight(params, placedb)
                        if self.ddp_rank == 0:
                            print("[I] density_weight = %.6E" % (model.density_weight.data))

                    optimizer.zero_grad()
                    
                    # Evaluation only on rank 0
                    if self.ddp_rank == 0 and cur_metric is not None:
                        # For shared parameter DDP, all GPUs have the same positions
                        cur_metric.evaluate(placedb, eval_ops, model.data_collections.pos[0])
                    
                    # update density weight and gamma (synchronized across all ranks)
                    if len(metrics) > 1 and self.ddp_rank == 0:
                        model.op_collections.update_density_weight_op(metrics)
                        model.op_collections.update_gamma_op(step, cur_metric.overflow)
                    
                    # Broadcast updated weights to all ranks
                    if self.ddp_world_size > 1:
                        dist.broadcast(model.density_weight, src=0)
                        dist.broadcast(model.gamma, src=0)
                    
                    if self.ddp_rank == 0 and cur_metric is not None:
                        cur_metric.density_weight = model.density_weight.data
                        cur_metric.gamma = model.gamma.data

                    # Forward and backward pass
                    if optimizer_name.lower() in ["sgd", "adam", "sgd_momentum", "sgd_nesterov", "cg"]:
                        model.obj_and_grad_fn(model.data_collections.pos[0])
                    elif optimizer_name.lower() != "nesterov":
                        assert 0, "unsupported optimizer %s" % (optimizer_name)

                    # stopping criteria (only check on rank 0)
                    should_stop = False
                    if self.ddp_rank == 0 and cur_metric is not None:
                        if iteration > 100 and ((cur_metric.overflow < params.stop_overflow and cur_metric.hpwl > metrics[-2].hpwl) or cur_metric.max_density < 1.0):
                            print("[D] stopping criteria: %d > 100 and (( %g < 0.1 and %g > %g ) or %g < 1.0)" % (iteration, cur_metric.overflow, cur_metric.hpwl, metrics[-2].hpwl, cur_metric.max_density))
                            should_stop = True
                    
                    # Broadcast stopping decision to all ranks
                    if self.ddp_world_size > 1:
                        stop_tensor = torch.tensor([should_stop], dtype=torch.bool, device=self.device)
                        dist.broadcast(stop_tensor, src=0)
                        should_stop = stop_tensor.item()
                    
                    if should_stop:
                        break

                    # update learning rate
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = learning_rate

                    if self.ddp_rank == 0 and cur_metric is not None:
                        print(cur_metric)
                        
                    # plot placement (only on rank 0)
                    if params.plot_flag and iteration % 100 == 0 and self.ddp_rank == 0:
                        cur_pos = self.pos[0].data.clone().cpu().numpy()
                        self.plot(params, placedb, iteration, cur_pos)

                    t3 = time.time()
                    optimizer.step()
                    
                    if self.ddp_rank == 0:
                        print("[I] optimizer step %.3f ms" % ((time.time()-t3)*1000))
                        print("[I] full step %.3f ms" % ((time.time()-t0)*1000))

                    iteration += 1

                if self.ddp_rank == 0:
                    print("[I] optimizer %s takes %.3f seconds" % (optimizer_name, time.time()-tt))

        # legalization (only on rank 0 for now)
        if params.legalize_flag and self.ddp_rank == 0:
            tt = time.time()
            self.pos[0].data.copy_(self.op_collections.greedy_legalize_op(self.pos[0]))
            print("[I] legalization takes %.3f seconds" % (time.time()-tt))

        # detailed placement
        if params.detailed_place_flag:
            if self.ddp_rank == 0:
                print("[W] detailed placement NOT implemented yet, skipped")

        # save results (all ranks have the same positions)
        cur_pos = self.pos[0].data.clone().cpu().numpy()
            
        # Only rank 0 updates placedb
        if self.ddp_rank == 0:
            # assign solution
            placedb.node_x[:placedb.num_movable_nodes] = cur_pos[0:placedb.num_movable_nodes]
            placedb.node_y[:placedb.num_movable_nodes] = cur_pos[placedb.num_nodes:placedb.num_nodes+placedb.num_movable_nodes]
            # plot placement
            if params.plot_flag:
                self.plot(params, placedb, iteration, cur_pos)
                
        return metrics

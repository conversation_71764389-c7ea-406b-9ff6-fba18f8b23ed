# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Include any dependencies generated for this target.
include thirdparty/flute-3.1/CMakeFiles/flute-net.dir/depend.make

# Include the progress variables for this target.
include thirdparty/flute-3.1/CMakeFiles/flute-net.dir/progress.make

# Include the compile flags for this target's objects.
include thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flags.make

thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.o: thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.o: ../thirdparty/flute-3.1/flute-net.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute-net.dir/flute-net.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute-net.c

thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute-net.dir/flute-net.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute-net.c > CMakeFiles/flute-net.dir/flute-net.c.i

thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute-net.dir/flute-net.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute-net.c -o CMakeFiles/flute-net.dir/flute-net.c.s

# Object files for target flute-net
flute__net_OBJECTS = \
"CMakeFiles/flute-net.dir/flute-net.c.o"

# External object files for target flute-net
flute__net_EXTERNAL_OBJECTS =

thirdparty/flute-3.1/flute-net: thirdparty/flute-3.1/CMakeFiles/flute-net.dir/flute-net.c.o
thirdparty/flute-3.1/flute-net: thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build.make
thirdparty/flute-3.1/flute-net: thirdparty/flute-3.1/libflute.a
thirdparty/flute-3.1/flute-net: thirdparty/flute-3.1/CMakeFiles/flute-net.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable flute-net"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/flute-net.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build: thirdparty/flute-3.1/flute-net

.PHONY : thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build

thirdparty/flute-3.1/CMakeFiles/flute-net.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -P CMakeFiles/flute-net.dir/cmake_clean.cmake
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute-net.dir/clean

thirdparty/flute-3.1/CMakeFiles/flute-net.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1 /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1/CMakeFiles/flute-net.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute-net.dir/depend


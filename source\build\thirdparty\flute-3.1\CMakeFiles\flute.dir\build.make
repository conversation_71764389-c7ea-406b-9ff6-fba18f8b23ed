# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Include any dependencies generated for this target.
include thirdparty/flute-3.1/CMakeFiles/flute.dir/depend.make

# Include the progress variables for this target.
include thirdparty/flute-3.1/CMakeFiles/flute.dir/progress.make

# Include the compile flags for this target's objects.
include thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make

thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.o: ../thirdparty/flute-3.1/dist.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/dist.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/dist.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/dist.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/dist.c > CMakeFiles/flute.dir/dist.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/dist.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/dist.c -o CMakeFiles/flute.dir/dist.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.o: ../thirdparty/flute-3.1/dl.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/dl.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/dl.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/dl.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/dl.c > CMakeFiles/flute.dir/dl.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/dl.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/dl.c -o CMakeFiles/flute.dir/dl.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.o: ../thirdparty/flute-3.1/err.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/err.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/err.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/err.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/err.c > CMakeFiles/flute.dir/err.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/err.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/err.c -o CMakeFiles/flute.dir/err.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.o: ../thirdparty/flute-3.1/heap.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/heap.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/heap.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/heap.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/heap.c > CMakeFiles/flute.dir/heap.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/heap.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/heap.c -o CMakeFiles/flute.dir/heap.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.o: ../thirdparty/flute-3.1/mst2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/mst2.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/mst2.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/mst2.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/mst2.c > CMakeFiles/flute.dir/mst2.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/mst2.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/mst2.c -o CMakeFiles/flute.dir/mst2.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.o: ../thirdparty/flute-3.1/neighbors.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/neighbors.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/neighbors.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/neighbors.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/neighbors.c > CMakeFiles/flute.dir/neighbors.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/neighbors.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/neighbors.c -o CMakeFiles/flute.dir/neighbors.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.o: ../thirdparty/flute-3.1/bookshelf_IO.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/bookshelf_IO.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/bookshelf_IO.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/bookshelf_IO.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/bookshelf_IO.c > CMakeFiles/flute.dir/bookshelf_IO.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/bookshelf_IO.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/bookshelf_IO.c -o CMakeFiles/flute.dir/bookshelf_IO.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.o: ../thirdparty/flute-3.1/memAlloc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/memAlloc.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/memAlloc.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/memAlloc.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/memAlloc.c > CMakeFiles/flute.dir/memAlloc.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/memAlloc.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/memAlloc.c -o CMakeFiles/flute.dir/memAlloc.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.o: ../thirdparty/flute-3.1/flute.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/flute.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/flute.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute.c > CMakeFiles/flute.dir/flute.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/flute.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute.c -o CMakeFiles/flute.dir/flute.c.s

thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.o: thirdparty/flute-3.1/CMakeFiles/flute.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.o: ../thirdparty/flute-3.1/flute_mst.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/flute.dir/flute_mst.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute_mst.c

thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/flute.dir/flute_mst.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute_mst.c > CMakeFiles/flute.dir/flute_mst.c.i

thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/flute.dir/flute_mst.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/flute_mst.c -o CMakeFiles/flute.dir/flute_mst.c.s

# Object files for target flute
flute_OBJECTS = \
"CMakeFiles/flute.dir/dist.c.o" \
"CMakeFiles/flute.dir/dl.c.o" \
"CMakeFiles/flute.dir/err.c.o" \
"CMakeFiles/flute.dir/heap.c.o" \
"CMakeFiles/flute.dir/mst2.c.o" \
"CMakeFiles/flute.dir/neighbors.c.o" \
"CMakeFiles/flute.dir/bookshelf_IO.c.o" \
"CMakeFiles/flute.dir/memAlloc.c.o" \
"CMakeFiles/flute.dir/flute.c.o" \
"CMakeFiles/flute.dir/flute_mst.c.o"

# External object files for target flute
flute_EXTERNAL_OBJECTS =

thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/dist.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/dl.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/err.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/heap.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/mst2.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/neighbors.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/bookshelf_IO.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/memAlloc.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/flute.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/flute_mst.c.o
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make
thirdparty/flute-3.1/libflute.a: thirdparty/flute-3.1/CMakeFiles/flute.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Linking C static library libflute.a"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -P CMakeFiles/flute.dir/cmake_clean_target.cmake
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/flute.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
thirdparty/flute-3.1/CMakeFiles/flute.dir/build: thirdparty/flute-3.1/libflute.a

.PHONY : thirdparty/flute-3.1/CMakeFiles/flute.dir/build

thirdparty/flute-3.1/CMakeFiles/flute.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -P CMakeFiles/flute.dir/cmake_clean.cmake
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute.dir/clean

thirdparty/flute-3.1/CMakeFiles/flute.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1 /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1/CMakeFiles/flute.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : thirdparty/flute-3.1/CMakeFiles/flute.dir/depend


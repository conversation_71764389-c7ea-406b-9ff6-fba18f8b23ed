##
# @file   setup.py.in
# <AUTHOR> Li
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension, CUDAExtension
import os
import sys
import copy
import sysconfig
utility_dir = "${UTILITY_LIBRARY_DIRS}"
ops_dir = "${OPS_DIR}"

hip_arch = '${CMAKE_HIP_FLAGS}'
print("hip_arch = %s" % (hip_arch))

include_dirs = [ops_dir]
lib_dirs = [utility_dir]
libs = ['utility']

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

def add_prefix(filename):
    return os.path.join('${CMAKE_CURRENT_SOURCE_DIR}/src', filename)

modules = []

try:
    from torch.utils.cpp_extension import ROCM_HOME
    is_rocm_pytorch = True if ((torch.version.hip is not None) and (ROCM_HOME is not None)) else False
except importError:
    pass

modules.extend([
    CppExtension('electric_potential_cpp',
        [
            add_prefix('electric_density_map.cpp'),
            add_prefix('electric_force.cpp')
            ],
        include_dirs=copy.deepcopy(include_dirs),
        library_dirs=copy.deepcopy(lib_dirs),
        libraries=copy.deepcopy(libs),
        extra_compile_args={
            'cxx' : [torch_major_version, torch_minor_version, '-fopenmp']
            },
        runtime_library_dirs=[python_lib] if python_lib else []
        ),
    ])

if is_rocm_pytorch == True:
    modules.extend([
            CUDAExtension('electric_potential_hip',
                [
                    add_prefix('electric_density_map_hip.cpp'),
                    add_prefix('electric_density_map_hip_kernel.hip'),
                    add_prefix('electric_force_hip.cpp'),
                    add_prefix('electric_force_hip_kernel.hip'),
                    ],
                include_dirs=copy.deepcopy(include_dirs),
                library_dirs=copy.deepcopy(lib_dirs),
                libraries=['hipsparse'] + copy.deepcopy(libs),
                extra_compile_args={
                    'cxx': ['-O3', torch_major_version, torch_minor_version],
                    'hipcc': ['-O3', hip_arch, '-fgpu-rdc']
                    },
                runtime_library_dirs=[python_lib] if python_lib else []
                ),
        ])

setup(
        name='electric_potential',
        ext_modules=modules,
        cmdclass={
            'build_ext': BuildExtension
            })

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for thirdparty.

# Include the progress variables for this target.
include thirdparty/CMakeFiles/thirdparty.dir/progress.make

thirdparty/CMakeFiles/thirdparty: thirdparty/flute-3.1/flute.stamp
thirdparty/CMakeFiles/thirdparty: thirdparty/Limbo/Limbo.stamp


thirdparty/flute-3.1/flute.stamp: ../thirdparty/flute-3.1/*.c
thirdparty/flute-3.1/flute.stamp: ../thirdparty/flute-3.1/*.h
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating flute-3.1/flute.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1/flute.stamp

thirdparty/Limbo/Limbo.stamp:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating Limbo/Limbo.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty && /usr/bin/make CXX=/usr/bin/c++ CC=/usr/bin/cc FC=/usr/bin/gfortran CXXSTD="-std=c++11 -D_GLIBCXX_USE_CXX11_ABI=1" BOOST_DIR=/opt/myboost/boost_1_62_0 -C /public/home/<USER>/DREAMPlace/source/thirdparty/Limbo
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty && /usr/bin/make install -C /public/home/<USER>/DREAMPlace/source/thirdparty/Limbo PREFIX=/public/home/<USER>/DREAMPlace/source/build/thirdparty/Limbo
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/thirdparty/Limbo/Limbo.stamp

thirdparty: thirdparty/CMakeFiles/thirdparty
thirdparty: thirdparty/flute-3.1/flute.stamp
thirdparty: thirdparty/Limbo/Limbo.stamp
thirdparty: thirdparty/CMakeFiles/thirdparty.dir/build.make

.PHONY : thirdparty

# Rule to build all files generated by this target.
thirdparty/CMakeFiles/thirdparty.dir/build: thirdparty

.PHONY : thirdparty/CMakeFiles/thirdparty.dir/build

thirdparty/CMakeFiles/thirdparty.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty && $(CMAKE_COMMAND) -P CMakeFiles/thirdparty.dir/cmake_clean.cmake
.PHONY : thirdparty/CMakeFiles/thirdparty.dir/clean

thirdparty/CMakeFiles/thirdparty.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/thirdparty /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/thirdparty /public/home/<USER>/DREAMPlace/source/build/thirdparty/CMakeFiles/thirdparty.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : thirdparty/CMakeFiles/thirdparty.dir/depend


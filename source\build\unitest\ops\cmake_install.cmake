# Install script for directory: /public/home/<USER>/DREAMPlace/source/unitest/ops

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/opt/software/HOCPlace")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/unitest/ops" TYPE FILE FILES
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/dct_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/density_overflow_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/density_potential_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/draw_place_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/electric_potential_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/hpwl_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/logsumexp_wirelength_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/move_boundary_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/rmst_wl_unitest.py"
    "/public/home/<USER>/DREAMPlace/source/unitest/ops/weighted_average_wirelength_unitest.py"
    )
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("/public/home/<USER>/DREAMPlace/source/build/unitest/ops/place_io_unitest/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest/cmake_install.cmake")

endif()


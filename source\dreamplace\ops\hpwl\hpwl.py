##
# @file   hpwl.py
# <AUTHOR>
# @date   10 2024
#

import torch
from torch.autograd import Function
from torch import nn
import numpy as np
import pdb

import dreamplace.ops.hpwl.hpwl_cpp as hpwl_cpp
import dreamplace.ops.hpwl.hpwl_cpp_atomic as hpwl_cpp_atomic
try:
    import dreamplace.ops.hpwl.hpwl_hip as hpwl_hip
    import dreamplace.ops.hpwl.hpwl_hip_atomic as hpwl_hip_atomic
except:
    pass

class HPWLFunction(Function):
    """compute half-perimeter wirelength.
    @param pos pin location (x array, y array), not cell location
    @param flat_netpin flat netpin map, length of #pins
    @param netpin_start starting index in netpin map for each net, length of #nets+1, the last entry is #pins
    @param net_mask a boolean mask containing whether a net should be computed
    @param pin2net_map pin2net map, second set of options
    """
    @staticmethod
    def forward(ctx, pos, flat_netpin, netpin_start, net_mask, num_threads):
        output = pos.new_empty(1)
        # Convert tensors to int32 for C++/HIP extension compatibility
        flat_netpin_int32 = flat_netpin.int() if flat_netpin.dtype != torch.int32 else flat_netpin
        netpin_start_int32 = netpin_start.int() if netpin_start.dtype != torch.int32 else netpin_start

        if pos.is_cuda:
            output = hpwl_hip.forward(pos.view(pos.numel()), flat_netpin_int32, netpin_start_int32, net_mask)
        else:
            output = hpwl_cpp.forward(pos.view(pos.numel()), flat_netpin_int32, netpin_start_int32, net_mask, num_threads)
        return output

class HPWLAtomicFunction(Function):
    """compute half-perimeter wirelength using atomic max/min.
    @param pos pin location (x array, y array), not cell location
    @param pin2net_map pin2net map, second set of options
    @param net_mask a boolean mask containing whether a net should be computed
    """
    @staticmethod
    def forward(ctx, pos, pin2net_map, net_mask):
        output = pos.new_empty(1)
        # Convert pin2net_map to int32 for C++/HIP extension compatibility
        pin2net_map_int32 = pin2net_map.int() if pin2net_map.dtype != torch.int32 else pin2net_map

        if pos.is_cuda:
            output = hpwl_hip_atomic.forward(pos.view(pos.numel()), pin2net_map_int32, net_mask)
        else:
            output = hpwl_cpp_atomic.forward(pos.view(pos.numel()), pin2net_map_int32, net_mask)
        return output

class HPWL(nn.Module):
    """
    @brief Compute half-perimeter wirelength.
    Support two algoriths: net-by-net and atomic.
    Different parameters are required for different algorithms.
    """
    def __init__(self, flat_netpin=None, netpin_start=None, pin2net_map=None, net_mask=None, algorithm='atomic', num_threads=8):
        """
        @brief initialization
        @param flat_netpin flat netpin map, length of #pins
        @param netpin_start starting index in netpin map for each net, length of #nets+1, the last entry is #pins
        @param pin2net_map pin2net map
        @param net_mask whether to compute wirelength, 1 means to compute, 0 means to ignore
        @param algorithm must be net-by-net | atomic
        """
        super(HPWL, self).__init__()
        assert net_mask is not None, "net_mask is a requried parameter"
        if algorithm == 'net-by-net':
            assert flat_netpin is not None and netpin_start is not None, "flat_netpin, netpin_start are requried parameters for algorithm net-by-net"
        elif algorithm == 'atomic':
            assert pin2net_map is not None, "pin2net_map is required for algorithm atomic"
        self.flat_netpin = flat_netpin
        self.netpin_start = netpin_start
        self.pin2net_map = pin2net_map
        self.net_mask = net_mask
        self.algorithm = algorithm
        self.num_threads = num_threads
    def forward(self, pos):
        if self.algorithm == 'net-by-net':
            return HPWLFunction.apply(pos,
                    self.flat_netpin,
                    self.netpin_start,
                    self.net_mask,
                    self.num_threads
                    )
        elif self.algorithm == 'atomic':
            return HPWLAtomicFunction.apply(pos,
                    self.pin2net_map,
                    self.net_mask
                    )

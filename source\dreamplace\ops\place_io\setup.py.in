##
# @file   setup.py.in
# <AUTHOR>
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

import os
import sys
from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension
import copy
import sysconfig
limbo_dir = "${LIMBO_DIR}"

include_dirs = [os.path.join(os.path.abspath(limbo_dir), 'include'), '${OPS_DIR}', '${Boost_INCLUDE_DIRS}', '${ZLIB_INCLUDE_DIRS}']
lib_dirs = [os.path.join(os.path.abspath(limbo_dir), 'lib'), '${Boost_LIBRARY_DIRS}', os.path.dirname('${ZLIB_LIBRARIES}'), '${UTILITY_LIBRARY_DIRS}']
libs = ['lefparseradapt', 'defparseradapt', 'verilogparser', 'gdsparser', 'bookshelfparser', 'programoptions', 'boost_system', 'boost_timer', 'boost_chrono', 'boost_iostreams', 'z', 'utility']

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

def add_prefix(filename):
    return os.path.join('${CMAKE_CURRENT_SOURCE_DIR}/src', filename)

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

setup(
        name='place_io',
        ext_modules=[
            CppExtension('place_io_cpp',
                [
                    add_prefix('place_io.cpp'),
                    add_prefix('BenchMetrics.cpp'),
                    add_prefix('BinMap.cpp'),
                    add_prefix('Enums.cpp'),
                    add_prefix('Net.cpp'),
                    add_prefix('Node.cpp'),
                    add_prefix('Params.cpp'),
                    add_prefix('PlaceDB.cpp'),
                    add_prefix('DefWriter.cpp'),
                    add_prefix('BookshelfWriter.cpp')
                    ],
                include_dirs=copy.deepcopy(include_dirs),
                library_dirs=copy.deepcopy(lib_dirs),
                libraries=copy.deepcopy(libs),
                extra_compile_args={
                    'cxx': ['-fvisibility=hidden', torch_major_version, torch_minor_version],
                    },
                runtime_library_dirs=[python_lib] if python_lib else []
                ),
            ],
        cmdclass={
            'build_ext': BuildExtension
            })

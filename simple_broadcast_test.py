#!/usr/bin/env python3
##
# @file   simple_broadcast_test.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Simple test for broadcast timeout fix
#

import os
import sys
import torch

# Add source directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'source'))

def test_import():
    """Test if the new functions can be imported"""
    try:
        from dreamplace.ddp_shared_param_utils import broadcast_tensor_with_timeout
        print("✅ Successfully imported broadcast_tensor_with_timeout")
        return True
    except ImportError as e:
        print(f"❌ Failed to import broadcast_tensor_with_timeout: {e}")
        return False

def test_single_process():
    """Test broadcast function in single process mode"""
    try:
        from dreamplace.ddp_shared_param_utils import broadcast_tensor_with_timeout
        
        # Create a test tensor
        test_tensor = torch.tensor([42.0])
        original_value = test_tensor.item()
        
        # Test broadcast in single process (should be no-op)
        result_tensor = broadcast_tensor_with_timeout(test_tensor, src=0, timeout_seconds=10)
        
        if result_tensor.item() == original_value:
            print("✅ Single process broadcast test passed")
            return True
        else:
            print(f"❌ Single process broadcast test failed: expected {original_value}, got {result_tensor.item()}")
            return False
            
    except Exception as e:
        print(f"❌ Single process broadcast test error: {e}")
        return False

def test_timeout_parameters():
    """Test that timeout parameters are accepted"""
    try:
        from dreamplace.ddp_shared_param_utils import broadcast_tensor_with_timeout
        
        test_tensor = torch.tensor([1.0])
        
        # Test different timeout values
        for timeout in [1, 10, 60, 300]:
            result = broadcast_tensor_with_timeout(test_tensor.clone(), src=0, timeout_seconds=timeout)
            if result.item() != 1.0:
                print(f"❌ Timeout parameter test failed for timeout={timeout}")
                return False
        
        print("✅ Timeout parameter test passed")
        return True
        
    except Exception as e:
        print(f"❌ Timeout parameter test error: {e}")
        return False

def test_nccl_settings():
    """Test NCCL environment variable settings"""
    try:
        from dreamplace.ddp_shared_param_utils import apply_performance_optimizations
        
        # Apply optimizations
        apply_performance_optimizations()
        
        # Check if NCCL settings are applied
        expected_settings = {
            'NCCL_TIMEOUT': '1800',
            'NCCL_BLOCKING_WAIT': '1',
            'NCCL_ASYNC_ERROR_HANDLING': '1',
            'NCCL_DEBUG': 'WARN'
        }
        
        all_set = True
        for key, expected_value in expected_settings.items():
            actual_value = os.environ.get(key)
            if actual_value != expected_value:
                print(f"❌ NCCL setting {key}: expected {expected_value}, got {actual_value}")
                all_set = False
        
        if all_set:
            print("✅ NCCL settings test passed")
            return True
        else:
            print("❌ Some NCCL settings were not applied correctly")
            return False
            
    except Exception as e:
        print(f"❌ NCCL settings test error: {e}")
        return False

def main():
    """Run all tests"""
    print("Running simple broadcast timeout fix tests...")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_import),
        ("Single Process Test", test_single_process),
        ("Timeout Parameters Test", test_timeout_parameters),
        ("NCCL Settings Test", test_nccl_settings)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"Test {test_name} failed")
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The broadcast timeout fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

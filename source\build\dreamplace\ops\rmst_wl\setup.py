##
# @file   setup.py.in
# <AUTHOR>
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

import os
from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension, CUDAExtension
import copy
import sysconfig
utility_dir = "/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility"
ops_dir = '/public/home/<USER>/DREAMPlace/source/dreamplace/ops'

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

def add_prefix(filename):
    return os.path.join('/public/home/<USER>/DREAMPlace/source/dreamplace/ops/rmst_wl/src', filename)

modules = []
lib_dirs = []
libs = []

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

modules.extend([
    CppExtension('rmst_wl_cpp',
        [
            add_prefix('rmst_wl.cpp')
            ],
        include_dirs=['/public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1', ops_dir],
        library_dirs=['/public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1', utility_dir] + copy.deepcopy(lib_dirs),
        libraries=['flute', 'utility'] + copy.deepcopy(libs),
        extra_compile_args={
            'cxx' : [torch_major_version, torch_minor_version]
            },
        runtime_library_dirs=[python_lib] if python_lib else []
        ),
    ])

setup(
        name='rmst_wl',
        ext_modules=modules,
        cmdclass={
            'build_ext': BuildExtension
            })

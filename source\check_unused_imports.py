#!/usr/bin/env python3
##
# @file   check_unused_imports.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Check for unused imports in shared parameter DDP files
#

import os
import re
import sys

def check_file_imports(filepath):
    """Check for unused imports in a Python file"""
    print(f"\n=== Checking {filepath} ===")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return
    
    # Extract imports
    import_lines = []
    for line_num, line in enumerate(content.split('\n'), 1):
        line = line.strip()
        if line.startswith('import ') or line.startswith('from '):
            import_lines.append((line_num, line))
    
    print(f"Found {len(import_lines)} import statements:")
    
    # Check each import
    for line_num, import_line in import_lines:
        print(f"  Line {line_num}: {import_line}")
        
        # Extract imported names
        imported_names = extract_imported_names(import_line)
        
        # Check if each name is used
        unused_names = []
        for name in imported_names:
            if not is_name_used(content, name, import_line):
                unused_names.append(name)
        
        if unused_names:
            print(f"    ❌ UNUSED: {', '.join(unused_names)}")
        else:
            print(f"    ✅ All imports used")

def extract_imported_names(import_line):
    """Extract imported names from an import statement"""
    names = []
    
    if import_line.startswith('import '):
        # Handle: import module, import module as alias
        parts = import_line[7:].split(',')
        for part in parts:
            part = part.strip()
            if ' as ' in part:
                # import module as alias
                alias = part.split(' as ')[1].strip()
                names.append(alias)
            else:
                # import module
                module_name = part.strip()
                # Get the last part of the module name
                if '.' in module_name:
                    names.append(module_name.split('.')[-1])
                else:
                    names.append(module_name)
    
    elif import_line.startswith('from '):
        # Handle: from module import name1, name2
        if ' import ' in import_line:
            import_part = import_line.split(' import ')[1]
            parts = import_part.split(',')
            for part in parts:
                part = part.strip()
                if ' as ' in part:
                    # from module import name as alias
                    alias = part.split(' as ')[1].strip()
                    names.append(alias)
                else:
                    # from module import name
                    names.append(part.strip())
    
    return names

def is_name_used(content, name, import_line):
    """Check if a name is used in the content (excluding the import line itself)"""
    # Remove the import line from content for checking
    content_without_import = content.replace(import_line, '')
    
    # Simple regex to find usage of the name
    # Look for the name as a whole word (not part of another word)
    pattern = r'\b' + re.escape(name) + r'\b'
    
    return bool(re.search(pattern, content_without_import))

def main():
    """Main function to check all shared DDP files"""
    files_to_check = [
        'dreamplace/ddp_shared_param_utils.py',
        'dreamplace/BasicPlace_shared_ddp.py',
        'dreamplace/PlaceObj_shared_ddp.py',
        'dreamplace/NonLinearPlace_shared_ddp.py',
        'dreamplace/Placer_shared_ddp.py',
        'dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength_shared_ddp.py',
        'dreamplace/ops/electric_potential/electric_potential_shared_ddp.py',
        'dreamplace/ops/electric_potential/electric_overflow_shared_ddp.py',
        'dreamplace/ops/hpwl/hpwl_shared_ddp.py',
        'test_shared_ddp.py',
        'test_ddp_setup.py',
        'example_shared_ddp_usage.py'
    ]
    
    print("Checking for unused imports in shared parameter DDP files...")
    
    for filepath in files_to_check:
        if os.path.exists(filepath):
            check_file_imports(filepath)
        else:
            print(f"\n=== {filepath} ===")
            print(f"❌ File not found: {filepath}")
    
    print("\n" + "="*60)
    print("Import check completed!")
    print("="*60)

if __name__ == '__main__':
    main()

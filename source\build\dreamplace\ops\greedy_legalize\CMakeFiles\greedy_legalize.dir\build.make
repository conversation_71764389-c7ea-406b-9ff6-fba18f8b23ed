# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for greedy_legalize.

# Include the progress variables for this target.
include dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/progress.make

dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize: dreamplace/ops/greedy_legalize/greedy_legalize.stamp


dreamplace/ops/greedy_legalize/greedy_legalize.stamp: ../dreamplace/ops/greedy_legalize/src/bin_assignment_cpu.cpp
dreamplace/ops/greedy_legalize/greedy_legalize.stamp: ../dreamplace/ops/greedy_legalize/src/greedy_legalize.cpp
dreamplace/ops/greedy_legalize/greedy_legalize.stamp: ../dreamplace/ops/greedy_legalize/src/greedy_legalize_cpu.cpp
dreamplace/ops/greedy_legalize/greedy_legalize.stamp: ../dreamplace/ops/greedy_legalize/src/legalize_bin_cpu.cpp
dreamplace/ops/greedy_legalize/greedy_legalize.stamp: ../dreamplace/ops/greedy_legalize/src/merge_bin_cpu.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating greedy_legalize.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize/greedy_legalize.stamp

greedy_legalize: dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize
greedy_legalize: dreamplace/ops/greedy_legalize/greedy_legalize.stamp
greedy_legalize: dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/build.make

.PHONY : greedy_legalize

# Rule to build all files generated by this target.
dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/build: greedy_legalize

.PHONY : dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/build

dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize && $(CMAKE_COMMAND) -P CMakeFiles/greedy_legalize.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/clean

dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/greedy_legalize /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/depend


##
# @file   setup.py.in
# <AUTHOR>
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension, CUDAExtension

import os
import sys
import copy
import sysconfig
limbo_dir = "${LIMBO_DIR}"
ops_dir = "${OPS_DIR}"

include_dirs = [os.path.join(os.path.abspath(limbo_dir), 'include'), ops_dir, '${Boost_INCLUDE_DIRS}', '${ZLIB_INCLUDE_DIRS}']
lib_dirs = [os.path.join(os.path.abspath(limbo_dir), 'lib'), '${Boost_LIBRARY_DIRS}', os.path.dirname('${ZLIB_LIBRARIES}'), '${UTILITY_LIBRARY_DIRS}']
libs = ['gdsparser', 'boost_iostreams', 'z', 'utility']

if "${CAIRO_FOUND}".upper() == 'TRUE':
    print("found Cairo and enable")
    include_dirs.append('${CAIRO_INCLUDE_DIRS}')
    lib_dirs.append(os.path.dirname('${CAIRO_LIBRARIES}'))
    libs.append('cairo')
    cairo_compile_args = '-DDRAWPLACE=1'
else:
    print("not found Cairo and disable")
    cairo_compile_args = '-DDRAWPLACE=0'

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

def add_prefix(filename):
    return os.path.join('${CMAKE_CURRENT_SOURCE_DIR}/src', filename)

setup(
        name='draw_place',
        ext_modules=[
            CppExtension('draw_place_cpp',
                [
                    add_prefix('draw_place.cpp'),
                    ],
                include_dirs=copy.deepcopy(include_dirs),
                library_dirs=copy.deepcopy(lib_dirs),
                libraries=copy.deepcopy(libs),
                extra_compile_args={
                    'cxx': ['-fvisibility=hidden', cairo_compile_args, torch_major_version, torch_minor_version],
                    },
                runtime_library_dirs=[python_lib] if python_lib else []
                ),
            ],
        cmdclass={
            'build_ext': BuildExtension
            })

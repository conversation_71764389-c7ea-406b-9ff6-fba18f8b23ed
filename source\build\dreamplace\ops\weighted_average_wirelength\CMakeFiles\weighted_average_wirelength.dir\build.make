# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for weighted_average_wirelength.

# Include the progress variables for this target.
include dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/progress.make

dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength: dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp


dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength.cpp
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip.cpp
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_atomic.cpp
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_atomic_kernel.hip
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_atomic_kernel_hip.hip
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_kernel.hip
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_kernel_hip.hip
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_sparse.cpp
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_sparse_kernel.hip
dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp: ../dreamplace/ops/weighted_average_wirelength/src/weighted_average_wirelength_hip_sparse_kernel_hip.hip
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating weighted_average_wirelength.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp

weighted_average_wirelength: dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength
weighted_average_wirelength: dreamplace/ops/weighted_average_wirelength/weighted_average_wirelength.stamp
weighted_average_wirelength: dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/build.make

.PHONY : weighted_average_wirelength

# Rule to build all files generated by this target.
dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/build: weighted_average_wirelength

.PHONY : dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/build

dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength && $(CMAKE_COMMAND) -P CMakeFiles/weighted_average_wirelength.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/clean

dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/weighted_average_wirelength /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/depend


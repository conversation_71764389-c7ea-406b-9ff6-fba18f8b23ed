##
# @file   electric_overflow_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware electric overflow computation
#

import math
import numpy as np
import torch
import torch.distributed as dist
from torch import nn
from torch.autograd import Function
from torch.nn import functional as F

import dreamplace.ops.electric_potential.electric_potential_cpp as electric_potential_cpp

try:
    import dreamplace.ops.electric_potential.electric_potential_hip as electric_potential_hip
except:
    pass

from dreamplace.ddp_shared_param_utils import all_reduce_tensor_sum
import pdb
import matplotlib

matplotlib.use('Agg')
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.pyplot as plt


class ElectricOverflowSharedDDPFunction(Function):
    """
    @brief Shared parameter DDP-aware density overflow computation
    Each node's bin contribution is handled by only one GPU to avoid duplication
    """

    @staticmethod
    def forward(
            ctx,
            pos,
            node_size_x, node_size_y,
            bin_center_x, bin_center_y,
            initial_density_map,
            target_density,
            xl, yl, xh, yh,
            bin_size_x, bin_size_y,
            num_movable_nodes,
            num_filler_nodes,
            padding,
            padding_mask,  # same dimensions as density map, with padding regions to be 1
            num_bins_x,
            num_bins_y,
            num_movable_impacted_bins_x,
            num_movable_impacted_bins_y,
            num_filler_impacted_bins_x,
            num_filler_impacted_bins_y,
            local_node_mask,  # mask indicating which nodes this GPU processes
            num_threads,
            ddp_rank=0,
            ddp_world_size=1
    ):
        """
        @brief Forward pass for shared parameter DDP electric overflow
        @param local_node_mask mask indicating which nodes this GPU processes
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        
        # Create local position tensor with only assigned nodes
        local_pos = pos.clone()
        num_nodes = pos.shape[0] // 2
        
        # Zero out positions for nodes not assigned to this GPU
        node_mask_expanded = local_node_mask.bool()
        local_pos[:num_nodes][~node_mask_expanded] = 0.0
        local_pos[num_nodes:][~node_mask_expanded] = 0.0

        # Compute local density map (only for assigned nodes)
        if local_pos.is_cuda:
            local_density_output = electric_potential_hip.density_map(
                local_pos.view(local_pos.numel()),
                node_size_x, node_size_y,
                bin_center_x, bin_center_y,
                initial_density_map,
                target_density,
                xl, yl, xh, yh,
                bin_size_x, bin_size_y,
                num_movable_nodes,
                num_filler_nodes,
                padding,
                padding_mask,
                num_bins_x,
                num_bins_y,
                num_movable_impacted_bins_x,
                num_movable_impacted_bins_y,
                num_filler_impacted_bins_x,
                num_filler_impacted_bins_y
            )
        else:
            local_density_output = electric_potential_cpp.density_map(
                local_pos.view(local_pos.numel()),
                node_size_x, node_size_y,
                bin_center_x, bin_center_y,
                initial_density_map,
                target_density,
                xl, yl, xh, yh,
                bin_size_x, bin_size_y,
                num_movable_nodes,
                num_filler_nodes,
                padding,
                padding_mask,
                num_bins_x,
                num_bins_y,
                num_movable_impacted_bins_x,
                num_movable_impacted_bins_y,
                num_filler_impacted_bins_x,
                num_filler_impacted_bins_y,
                num_threads
            )

        # Get local density map
        local_density_map = local_density_output.view([num_bins_x, num_bins_y])
        
        # All-reduce to get global density map
        global_density_map = all_reduce_tensor_sum(local_density_map.clone())

        # Compute overflow metrics using global density map
        bin_area = bin_size_x * bin_size_y
        density_cost = (global_density_map - target_density * bin_area).clamp_(min=0.0).sum()
        max_density = global_density_map.max() / bin_area

        return density_cost, max_density


class ElectricOverflowSharedDDP(nn.Module):
    """
    @brief Shared parameter DDP-aware Electric Overflow computation
    Node positions are shared, but each node's bin contribution is handled by only one GPU
    """
    
    def __init__(self,
                 node_size_x, node_size_y,
                 bin_center_x, bin_center_y,
                 target_density,
                 xl, yl, xh, yh,
                 bin_size_x, bin_size_y,
                 num_movable_nodes,
                 num_terminals,
                 num_filler_nodes,
                 local_node_mask,  # mask indicating which nodes this GPU processes
                 padding,
                 num_threads=8,
                 ddp_rank=0,
                 ddp_world_size=1
                 ):
        """
        @brief Initialize shared parameter DDP Electric Overflow
        @param local_node_mask mask indicating which nodes this GPU processes
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs  
        """
        super(ElectricOverflowSharedDDP, self).__init__()
        
        # Store DDP parameters
        self.ddp_rank = ddp_rank
        self.ddp_world_size = ddp_world_size
        self.local_node_mask = local_node_mask
        
        # Store all other parameters (same as original)
        self.node_size_x = node_size_x
        self.node_size_y = node_size_y
        self.bin_center_x = bin_center_x
        self.bin_center_y = bin_center_y
        self.target_density = target_density
        self.xl = xl
        self.yl = yl
        self.xh = xh
        self.yh = yh
        self.bin_size_x = torch.tensor(bin_size_x, dtype=node_size_x.dtype, device=node_size_x.device)
        self.bin_size_y = torch.tensor(bin_size_y, dtype=node_size_y.dtype, device=node_size_y.device)
        self.num_movable_nodes = num_movable_nodes
        self.num_terminals = num_terminals
        self.num_filler_nodes = num_filler_nodes
        self.padding = padding
        
        # Compute bin parameters (same as original)
        self.num_bins_x = int(math.ceil((xh - xl) / bin_size_x))
        self.num_bins_y = int(math.ceil((yh - yl) / bin_size_y))
        sqrt2 = 1.414213562
        
        self.num_movable_impacted_bins_x = ((node_size_x[
                                             :num_movable_nodes].max() + 2 * sqrt2 * self.bin_size_x) / self.bin_size_x).ceil().clamp(
            max=self.num_bins_x)
        self.num_movable_impacted_bins_y = ((node_size_y[
                                             :num_movable_nodes].max() + 2 * sqrt2 * self.bin_size_y) / self.bin_size_y).ceil().clamp(
            max=self.num_bins_y)
            
        if num_filler_nodes:
            self.num_filler_impacted_bins_x = ((node_size_x[
                                                -num_filler_nodes:].max() + 2 * sqrt2 * self.bin_size_x) / self.bin_size_x).ceil().clamp(
                max=self.num_bins_x)
            self.num_filler_impacted_bins_y = ((node_size_y[
                                                -num_filler_nodes:].max() + 2 * sqrt2 * self.bin_size_y) / self.bin_size_y).ceil().clamp(
                max=self.num_bins_y)
        else:
            self.num_filler_impacted_bins_x = 0
            self.num_filler_impacted_bins_y = 0
            
        if self.padding > 0:
            self.padding_mask = torch.ones(self.num_bins_x, self.num_bins_y, dtype=torch.uint8,
                                           device=node_size_x.device)
            self.padding_mask[self.padding:self.num_bins_x - self.padding,
            self.padding:self.num_bins_y - self.padding].fill_(0)
        else:
            self.padding_mask = torch.zeros(self.num_bins_x, self.num_bins_y, dtype=torch.uint8,
                                            device=node_size_x.device)

        self.num_threads = num_threads

        # initial density_map due to fixed cells
        self.initial_density_map = None

    def forward(self, pos):
        """
        @brief Forward pass for shared parameter DDP electric overflow
        @param pos shared position tensor
        """
        # Initialize density map if needed (same as original)
        if self.initial_density_map is None:
            self._initialize_density_map(pos)

        return ElectricOverflowSharedDDPFunction.apply(
            pos,
            self.node_size_x, self.node_size_y,
            self.bin_center_x, self.bin_center_y,
            self.initial_density_map,
            self.target_density,
            self.xl, self.yl, self.xh, self.yh,
            self.bin_size_x, self.bin_size_y,
            self.num_movable_nodes,
            self.num_filler_nodes,
            self.padding,
            self.padding_mask,
            self.num_bins_x,
            self.num_bins_y,
            self.num_movable_impacted_bins_x,
            self.num_movable_impacted_bins_y,
            self.num_filler_impacted_bins_x,
            self.num_filler_impacted_bins_y,
            self.local_node_mask,  # Pass local node mask
            self.num_threads,
            self.ddp_rank,
            self.ddp_world_size
        )
    
    def _initialize_density_map(self, pos):
        """Initialize density map for fixed cells (same as original but DDP-aware)"""
        if self.num_terminals == 0:
            num_fixed_impacted_bins_x = 0
            num_fixed_impacted_bins_y = 0
        else:
            num_fixed_impacted_bins_x = ((self.node_size_x[
                                          self.num_movable_nodes:self.num_movable_nodes + self.num_terminals].max() + self.bin_size_x) / self.bin_size_x).ceil().clamp(
                max=self.num_bins_x)
            num_fixed_impacted_bins_y = ((self.node_size_y[
                                          self.num_movable_nodes:self.num_movable_nodes + self.num_terminals].max() + self.bin_size_y) / self.bin_size_y).ceil().clamp(
                max=self.num_bins_y)
                
        # For shared parameter DDP, use full positions for fixed density map
        if pos.is_cuda:
            self.initial_density_map = electric_potential_hip.fixed_density_map(
                pos.view(pos.numel()),
                self.node_size_x, self.node_size_y,
                self.bin_center_x, self.bin_center_y,
                self.xl, self.yl, self.xh, self.yh,
                self.bin_size_x, self.bin_size_y,
                self.num_movable_nodes,
                self.num_terminals,
                self.num_bins_x,
                self.num_bins_y,
                num_fixed_impacted_bins_x,
                num_fixed_impacted_bins_y
            )
        else:
            self.initial_density_map = electric_potential_cpp.fixed_density_map(
                pos.view(pos.numel()),
                self.node_size_x, self.node_size_y,
                self.bin_center_x, self.bin_center_y,
                self.xl, self.yl, self.xh, self.yh,
                self.bin_size_x, self.bin_size_y,
                self.num_movable_nodes,
                self.num_terminals,
                self.num_bins_x,
                self.num_bins_y,
                num_fixed_impacted_bins_x,
                num_fixed_impacted_bins_y,
                self.num_threads
            )
        # scale density of fixed macros
        self.initial_density_map.mul_(self.target_density)

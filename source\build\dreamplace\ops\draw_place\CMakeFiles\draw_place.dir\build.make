# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for draw_place.

# Include the progress variables for this target.
include dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/progress.make

dreamplace/ops/draw_place/CMakeFiles/draw_place: dreamplace/ops/draw_place/draw_place.stamp


dreamplace/ops/draw_place/draw_place.stamp: ../dreamplace/ops/draw_place/src/draw_place.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating draw_place.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place/draw_place.stamp

draw_place: dreamplace/ops/draw_place/CMakeFiles/draw_place
draw_place: dreamplace/ops/draw_place/draw_place.stamp
draw_place: dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/build.make

.PHONY : draw_place

# Rule to build all files generated by this target.
dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/build: draw_place

.PHONY : dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/build

dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place && $(CMAKE_COMMAND) -P CMakeFiles/draw_place.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/clean

dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/draw_place /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/depend


# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/opt/cmake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/opt/cmake/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/opt/cmake/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/cmake/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/opt/cmake/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /public/home/<USER>/DREAMPlace/source/build/CMakeFiles /public/home/<USER>/DREAMPlace/source/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /public/home/<USER>/DREAMPlace/source/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named thirdparty

# Build rule for target.
thirdparty: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 thirdparty
.PHONY : thirdparty

# fast build rule for target.
thirdparty/fast:
	$(MAKE) -f thirdparty/CMakeFiles/thirdparty.dir/build.make thirdparty/CMakeFiles/thirdparty.dir/build
.PHONY : thirdparty/fast

#=============================================================================
# Target rules for targets named flute

# Build rule for target.
flute: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 flute
.PHONY : flute

# fast build rule for target.
flute/fast:
	$(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute.dir/build
.PHONY : flute/fast

#=============================================================================
# Target rules for targets named rand-pts

# Build rule for target.
rand-pts: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 rand-pts
.PHONY : rand-pts

# fast build rule for target.
rand-pts/fast:
	$(MAKE) -f thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build.make thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build
.PHONY : rand-pts/fast

#=============================================================================
# Target rules for targets named flute-net

# Build rule for target.
flute-net: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 flute-net
.PHONY : flute-net

# fast build rule for target.
flute-net/fast:
	$(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-net.dir/build
.PHONY : flute-net/fast

#=============================================================================
# Target rules for targets named flute-ckt

# Build rule for target.
flute-ckt: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 flute-ckt
.PHONY : flute-ckt

# fast build rule for target.
flute-ckt/fast:
	$(MAKE) -f thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build.make thirdparty/flute-3.1/CMakeFiles/flute-ckt.dir/build
.PHONY : flute-ckt/fast

#=============================================================================
# Target rules for targets named utility

# Build rule for target.
utility: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 utility
.PHONY : utility

# fast build rule for target.
utility/fast:
	$(MAKE) -f dreamplace/ops/utility/CMakeFiles/utility.dir/build.make dreamplace/ops/utility/CMakeFiles/utility.dir/build
.PHONY : utility/fast

#=============================================================================
# Target rules for targets named clean_dct

# Build rule for target.
clean_dct: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_dct
.PHONY : clean_dct

# fast build rule for target.
clean_dct/fast:
	$(MAKE) -f dreamplace/ops/dct/CMakeFiles/clean_dct.dir/build.make dreamplace/ops/dct/CMakeFiles/clean_dct.dir/build
.PHONY : clean_dct/fast

#=============================================================================
# Target rules for targets named dct

# Build rule for target.
dct: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 dct
.PHONY : dct

# fast build rule for target.
dct/fast:
	$(MAKE) -f dreamplace/ops/dct/CMakeFiles/dct.dir/build.make dreamplace/ops/dct/CMakeFiles/dct.dir/build
.PHONY : dct/fast

#=============================================================================
# Target rules for targets named clean_density_overflow

# Build rule for target.
clean_density_overflow: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_density_overflow
.PHONY : clean_density_overflow

# fast build rule for target.
clean_density_overflow/fast:
	$(MAKE) -f dreamplace/ops/density_overflow/CMakeFiles/clean_density_overflow.dir/build.make dreamplace/ops/density_overflow/CMakeFiles/clean_density_overflow.dir/build
.PHONY : clean_density_overflow/fast

#=============================================================================
# Target rules for targets named density_overflow

# Build rule for target.
density_overflow: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 density_overflow
.PHONY : density_overflow

# fast build rule for target.
density_overflow/fast:
	$(MAKE) -f dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/build.make dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/build
.PHONY : density_overflow/fast

#=============================================================================
# Target rules for targets named clean_density_potential

# Build rule for target.
clean_density_potential: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_density_potential
.PHONY : clean_density_potential

# fast build rule for target.
clean_density_potential/fast:
	$(MAKE) -f dreamplace/ops/density_potential/CMakeFiles/clean_density_potential.dir/build.make dreamplace/ops/density_potential/CMakeFiles/clean_density_potential.dir/build
.PHONY : clean_density_potential/fast

#=============================================================================
# Target rules for targets named density_potential

# Build rule for target.
density_potential: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 density_potential
.PHONY : density_potential

# fast build rule for target.
density_potential/fast:
	$(MAKE) -f dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/build.make dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/build
.PHONY : density_potential/fast

#=============================================================================
# Target rules for targets named clean_logsumexp_wirelength

# Build rule for target.
clean_logsumexp_wirelength: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_logsumexp_wirelength
.PHONY : clean_logsumexp_wirelength

# fast build rule for target.
clean_logsumexp_wirelength/fast:
	$(MAKE) -f dreamplace/ops/logsumexp_wirelength/CMakeFiles/clean_logsumexp_wirelength.dir/build.make dreamplace/ops/logsumexp_wirelength/CMakeFiles/clean_logsumexp_wirelength.dir/build
.PHONY : clean_logsumexp_wirelength/fast

#=============================================================================
# Target rules for targets named logsumexp_wirelength

# Build rule for target.
logsumexp_wirelength: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 logsumexp_wirelength
.PHONY : logsumexp_wirelength

# fast build rule for target.
logsumexp_wirelength/fast:
	$(MAKE) -f dreamplace/ops/logsumexp_wirelength/CMakeFiles/logsumexp_wirelength.dir/build.make dreamplace/ops/logsumexp_wirelength/CMakeFiles/logsumexp_wirelength.dir/build
.PHONY : logsumexp_wirelength/fast

#=============================================================================
# Target rules for targets named clean_draw_place

# Build rule for target.
clean_draw_place: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_draw_place
.PHONY : clean_draw_place

# fast build rule for target.
clean_draw_place/fast:
	$(MAKE) -f dreamplace/ops/draw_place/CMakeFiles/clean_draw_place.dir/build.make dreamplace/ops/draw_place/CMakeFiles/clean_draw_place.dir/build
.PHONY : clean_draw_place/fast

#=============================================================================
# Target rules for targets named draw_place

# Build rule for target.
draw_place: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 draw_place
.PHONY : draw_place

# fast build rule for target.
draw_place/fast:
	$(MAKE) -f dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/build.make dreamplace/ops/draw_place/CMakeFiles/draw_place.dir/build
.PHONY : draw_place/fast

#=============================================================================
# Target rules for targets named clean_electric_potential

# Build rule for target.
clean_electric_potential: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_electric_potential
.PHONY : clean_electric_potential

# fast build rule for target.
clean_electric_potential/fast:
	$(MAKE) -f dreamplace/ops/electric_potential/CMakeFiles/clean_electric_potential.dir/build.make dreamplace/ops/electric_potential/CMakeFiles/clean_electric_potential.dir/build
.PHONY : clean_electric_potential/fast

#=============================================================================
# Target rules for targets named electric_potential

# Build rule for target.
electric_potential: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 electric_potential
.PHONY : electric_potential

# fast build rule for target.
electric_potential/fast:
	$(MAKE) -f dreamplace/ops/electric_potential/CMakeFiles/electric_potential.dir/build.make dreamplace/ops/electric_potential/CMakeFiles/electric_potential.dir/build
.PHONY : electric_potential/fast

#=============================================================================
# Target rules for targets named clean_greedy_legalize

# Build rule for target.
clean_greedy_legalize: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_greedy_legalize
.PHONY : clean_greedy_legalize

# fast build rule for target.
clean_greedy_legalize/fast:
	$(MAKE) -f dreamplace/ops/greedy_legalize/CMakeFiles/clean_greedy_legalize.dir/build.make dreamplace/ops/greedy_legalize/CMakeFiles/clean_greedy_legalize.dir/build
.PHONY : clean_greedy_legalize/fast

#=============================================================================
# Target rules for targets named greedy_legalize

# Build rule for target.
greedy_legalize: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 greedy_legalize
.PHONY : greedy_legalize

# fast build rule for target.
greedy_legalize/fast:
	$(MAKE) -f dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/build.make dreamplace/ops/greedy_legalize/CMakeFiles/greedy_legalize.dir/build
.PHONY : greedy_legalize/fast

#=============================================================================
# Target rules for targets named clean_hpwl

# Build rule for target.
clean_hpwl: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_hpwl
.PHONY : clean_hpwl

# fast build rule for target.
clean_hpwl/fast:
	$(MAKE) -f dreamplace/ops/hpwl/CMakeFiles/clean_hpwl.dir/build.make dreamplace/ops/hpwl/CMakeFiles/clean_hpwl.dir/build
.PHONY : clean_hpwl/fast

#=============================================================================
# Target rules for targets named hpwl

# Build rule for target.
hpwl: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 hpwl
.PHONY : hpwl

# fast build rule for target.
hpwl/fast:
	$(MAKE) -f dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/build.make dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/build
.PHONY : hpwl/fast

#=============================================================================
# Target rules for targets named clean_move_boundary

# Build rule for target.
clean_move_boundary: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_move_boundary
.PHONY : clean_move_boundary

# fast build rule for target.
clean_move_boundary/fast:
	$(MAKE) -f dreamplace/ops/move_boundary/CMakeFiles/clean_move_boundary.dir/build.make dreamplace/ops/move_boundary/CMakeFiles/clean_move_boundary.dir/build
.PHONY : clean_move_boundary/fast

#=============================================================================
# Target rules for targets named move_boundary

# Build rule for target.
move_boundary: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 move_boundary
.PHONY : move_boundary

# fast build rule for target.
move_boundary/fast:
	$(MAKE) -f dreamplace/ops/move_boundary/CMakeFiles/move_boundary.dir/build.make dreamplace/ops/move_boundary/CMakeFiles/move_boundary.dir/build
.PHONY : move_boundary/fast

#=============================================================================
# Target rules for targets named clean_weighted_average_wirelength

# Build rule for target.
clean_weighted_average_wirelength: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_weighted_average_wirelength
.PHONY : clean_weighted_average_wirelength

# fast build rule for target.
clean_weighted_average_wirelength/fast:
	$(MAKE) -f dreamplace/ops/weighted_average_wirelength/CMakeFiles/clean_weighted_average_wirelength.dir/build.make dreamplace/ops/weighted_average_wirelength/CMakeFiles/clean_weighted_average_wirelength.dir/build
.PHONY : clean_weighted_average_wirelength/fast

#=============================================================================
# Target rules for targets named weighted_average_wirelength

# Build rule for target.
weighted_average_wirelength: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 weighted_average_wirelength
.PHONY : weighted_average_wirelength

# fast build rule for target.
weighted_average_wirelength/fast:
	$(MAKE) -f dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/build.make dreamplace/ops/weighted_average_wirelength/CMakeFiles/weighted_average_wirelength.dir/build
.PHONY : weighted_average_wirelength/fast

#=============================================================================
# Target rules for targets named clean_rmst_wl

# Build rule for target.
clean_rmst_wl: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_rmst_wl
.PHONY : clean_rmst_wl

# fast build rule for target.
clean_rmst_wl/fast:
	$(MAKE) -f dreamplace/ops/rmst_wl/CMakeFiles/clean_rmst_wl.dir/build.make dreamplace/ops/rmst_wl/CMakeFiles/clean_rmst_wl.dir/build
.PHONY : clean_rmst_wl/fast

#=============================================================================
# Target rules for targets named rmst_wl

# Build rule for target.
rmst_wl: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 rmst_wl
.PHONY : rmst_wl

# fast build rule for target.
rmst_wl/fast:
	$(MAKE) -f dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/build.make dreamplace/ops/rmst_wl/CMakeFiles/rmst_wl.dir/build
.PHONY : rmst_wl/fast

#=============================================================================
# Target rules for targets named clean_place_io

# Build rule for target.
clean_place_io: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 clean_place_io
.PHONY : clean_place_io

# fast build rule for target.
clean_place_io/fast:
	$(MAKE) -f dreamplace/ops/place_io/CMakeFiles/clean_place_io.dir/build.make dreamplace/ops/place_io/CMakeFiles/clean_place_io.dir/build
.PHONY : clean_place_io/fast

#=============================================================================
# Target rules for targets named place_io

# Build rule for target.
place_io: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 place_io
.PHONY : place_io

# fast build rule for target.
place_io/fast:
	$(MAKE) -f dreamplace/ops/place_io/CMakeFiles/place_io.dir/build.make dreamplace/ops/place_io/CMakeFiles/place_io.dir/build
.PHONY : place_io/fast

#=============================================================================
# Target rules for targets named abacus_unitest

# Build rule for target.
abacus_unitest: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 abacus_unitest
.PHONY : abacus_unitest

# fast build rule for target.
abacus_unitest/fast:
	$(MAKE) -f unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/build.make unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/build
.PHONY : abacus_unitest/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... list_install_components"
	@echo "... install"
	@echo "... thirdparty"
	@echo "... flute"
	@echo "... rand-pts"
	@echo "... flute-net"
	@echo "... flute-ckt"
	@echo "... utility"
	@echo "... clean_dct"
	@echo "... dct"
	@echo "... clean_density_overflow"
	@echo "... density_overflow"
	@echo "... clean_density_potential"
	@echo "... density_potential"
	@echo "... clean_logsumexp_wirelength"
	@echo "... logsumexp_wirelength"
	@echo "... clean_draw_place"
	@echo "... draw_place"
	@echo "... clean_electric_potential"
	@echo "... electric_potential"
	@echo "... clean_greedy_legalize"
	@echo "... greedy_legalize"
	@echo "... clean_hpwl"
	@echo "... hpwl"
	@echo "... clean_move_boundary"
	@echo "... move_boundary"
	@echo "... clean_weighted_average_wirelength"
	@echo "... weighted_average_wirelength"
	@echo "... clean_rmst_wl"
	@echo "... rmst_wl"
	@echo "... clean_place_io"
	@echo "... place_io"
	@echo "... abacus_unitest"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


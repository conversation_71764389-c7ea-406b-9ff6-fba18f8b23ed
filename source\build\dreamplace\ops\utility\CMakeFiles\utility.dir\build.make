# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Include any dependencies generated for this target.
include dreamplace/ops/utility/CMakeFiles/utility.dir/depend.make

# Include the progress variables for this target.
include dreamplace/ops/utility/CMakeFiles/utility.dir/progress.make

# Include the compile flags for this target's objects.
include dreamplace/ops/utility/CMakeFiles/utility.dir/flags.make

dreamplace/ops/utility/CMakeFiles/utility.dir/src/Msg.cpp.o: dreamplace/ops/utility/CMakeFiles/utility.dir/flags.make
dreamplace/ops/utility/CMakeFiles/utility.dir/src/Msg.cpp.o: ../dreamplace/ops/utility/src/Msg.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object dreamplace/ops/utility/CMakeFiles/utility.dir/src/Msg.cpp.o"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/utility.dir/src/Msg.cpp.o -c /public/home/<USER>/DREAMPlace/source/dreamplace/ops/utility/src/Msg.cpp

dreamplace/ops/utility/CMakeFiles/utility.dir/src/Msg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/utility.dir/src/Msg.cpp.i"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /public/home/<USER>/DREAMPlace/source/dreamplace/ops/utility/src/Msg.cpp > CMakeFiles/utility.dir/src/Msg.cpp.i

dreamplace/ops/utility/CMakeFiles/utility.dir/src/Msg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/utility.dir/src/Msg.cpp.s"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /public/home/<USER>/DREAMPlace/source/dreamplace/ops/utility/src/Msg.cpp -o CMakeFiles/utility.dir/src/Msg.cpp.s

# Object files for target utility
utility_OBJECTS = \
"CMakeFiles/utility.dir/src/Msg.cpp.o"

# External object files for target utility
utility_EXTERNAL_OBJECTS =

dreamplace/ops/utility/libutility.a: dreamplace/ops/utility/CMakeFiles/utility.dir/src/Msg.cpp.o
dreamplace/ops/utility/libutility.a: dreamplace/ops/utility/CMakeFiles/utility.dir/build.make
dreamplace/ops/utility/libutility.a: dreamplace/ops/utility/CMakeFiles/utility.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libutility.a"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility && $(CMAKE_COMMAND) -P CMakeFiles/utility.dir/cmake_clean_target.cmake
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/utility.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
dreamplace/ops/utility/CMakeFiles/utility.dir/build: dreamplace/ops/utility/libutility.a

.PHONY : dreamplace/ops/utility/CMakeFiles/utility.dir/build

dreamplace/ops/utility/CMakeFiles/utility.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility && $(CMAKE_COMMAND) -P CMakeFiles/utility.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/utility/CMakeFiles/utility.dir/clean

dreamplace/ops/utility/CMakeFiles/utility.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/utility /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility/CMakeFiles/utility.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/utility/CMakeFiles/utility.dir/depend


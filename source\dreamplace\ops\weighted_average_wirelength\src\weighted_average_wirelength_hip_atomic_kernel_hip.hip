#include <cfloat>
#include <stdio.h>
#include "assert.h"
#include "hip/hip_runtime.h"
#include "utility/src/print.h"
#include "utility/src/Msg.h"
#include "weighted_average_wirelength/src/functional_hip.h"

DREAMPLACE_BEGIN_NAMESPACE

template <typename T>
__global__ void computeExpSum(
        const T* exp_x,
        const int* pin2net_map,
        const unsigned char* net_mask,
        int num_nets,
        int num_pins,
        T* exp_x_sum
        )
{
    for (int i = blockIdx.x * blockDim.x + threadIdx.x; i < num_pins; i += blockDim.x * gridDim.x)
    {
        int net_id = pin2net_map[i];
        if (net_id >= 0 || net_mask[net_id])
        {
            atomicAdd(&exp_x_sum[net_id], exp_x[i]);
            __syncthreads();
        }
    }
}

template <typename T>
__global__ void computeXExpSum(
        const T* x,
        const T* exp_x,
        const int* pin2net_map,
        const unsigned char* net_mask,
        int num_nets,
        int num_pins,
        T* xexp_x_sum
        )
{
    for (int i =blockIdx.x * blockDim.x + threadIdx.x; i < num_pins; i += blockDim.x * gridDim.x)
    {
        int net_id = pin2net_map[i];
        if (net_id >= 0 || net_mask[net_id])
        {
            atomicAdd(&xexp_x_sum[net_id], x[i]*exp_x[i]);
            __syncthreads();
        }
    }
}

template <typename T, typename V>
int computeWeightedAverageWirelengthHipAtomicLauncher(
        const T* x, const T* y,
        const int* pin2net_map,
        const unsigned char* net_mask,
        int num_nets,
        int num_pins,
        const T* gamma,
        T* exp_xy, T* exp_nxy,
        T* exp_xy_sum, T* exp_nxy_sum,
        T* xyexp_xy_sum, T* xyexp_nxy_sum,
        V* xy_max, V* xy_min,
        T* partial_wl, // wirelength of each net
        const T* grad_tensor,
        T* grad_x_tensor, T* grad_y_tensor // the gradient is partial total wirelength to partial pin position
        )
{
    int thread_count = 256;
    int block_count = 128; // separate x and y

    hipError_t status;
    hipStream_t stream_x_exp;
    hipStream_t stream_nx_exp;
    hipStream_t stream_y_exp;
    hipStream_t stream_ny_exp;
    status = hipStreamCreate(&stream_x_exp);
    if (status != hipSuccess)
    {
        printf("hipStreamCreate failed for stream_x_exp\n");
        fflush(stdout);
        return 1;
    }
    status = hipStreamCreate(&stream_y_exp);
    if (status != hipSuccess)
    {
        printf("hipStreamCreate failed for stream_y_exp\n");
        fflush(stdout);
        return 1;
    }

    if (grad_tensor)
    {
       hipLaunchKernelGGL(( computeWeightedAverageWirelengthGrad), dim3(block_count), dim3(thread_count), 0, stream_x_exp, 
                x,
                exp_xy, exp_nxy,
                exp_xy_sum, exp_nxy_sum,
                xyexp_xy_sum, xyexp_nxy_sum,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                gamma,
                grad_tensor,
                grad_x_tensor
                );
       hipLaunchKernelGGL(( computeWeightedAverageWirelengthGrad), dim3(block_count), dim3(thread_count), 0, stream_y_exp, 
                y,
                exp_xy+num_pins, exp_nxy+num_pins,
                exp_xy_sum+num_nets, exp_nxy_sum+num_nets,
                xyexp_xy_sum+num_nets, xyexp_nxy_sum+num_nets,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                gamma,
                grad_tensor,
                grad_y_tensor
                );
    }
    else
    {
        status = hipStreamCreate(&stream_nx_exp);
        if (status != hipSuccess)
        {
            printf("hipStreamCreate failed for stream_nx_exp\n");
            fflush(stdout);
            return 1;
        }
        status = hipStreamCreate(&stream_ny_exp);
        if (status != hipSuccess)
        {
            printf("hipStreamCreate failed for stream_ny_exp\n");
            fflush(stdout);
            return 1;
        }

        // compute max/min
       hipLaunchKernelGGL(( computeMax), dim3(block_count), dim3(thread_count), 0, stream_x_exp, 
                x,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xy_max
                );
       hipLaunchKernelGGL(( computeMin), dim3(block_count), dim3(thread_count), 0, stream_nx_exp, 
                x,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xy_min
                );
       hipLaunchKernelGGL(( computeMax), dim3(block_count), dim3(thread_count), 0, stream_y_exp, 
                y,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xy_max+num_nets
                );
       hipLaunchKernelGGL(( computeMin), dim3(block_count), dim3(thread_count), 0, stream_ny_exp, 
                y,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xy_min+num_nets
                );

        // compute exp and negative exp
       hipLaunchKernelGGL(( computeExp), dim3(block_count), dim3(thread_count), 0, stream_x_exp, 
                x,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                gamma,
                xy_max,
                exp_xy
                );
       hipLaunchKernelGGL(( computeNegExp), dim3(block_count), dim3(thread_count), 0, stream_nx_exp, 
                x,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                gamma,
                xy_min,
                exp_nxy
                );
       hipLaunchKernelGGL(( computeExp), dim3(block_count), dim3(thread_count), 0, stream_y_exp, 
                y,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                gamma,
                xy_max+num_nets,
                exp_xy+num_pins
                );
       hipLaunchKernelGGL(( computeNegExp), dim3(block_count), dim3(thread_count), 0, stream_ny_exp, 
                y,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                gamma,
                xy_min+num_nets,
                exp_nxy+num_pins
                );

        // compute exp sum
       hipLaunchKernelGGL(( computeExpSum), dim3(block_count), dim3(thread_count), 0, stream_x_exp, 
                exp_xy,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                exp_xy_sum
                );
       hipLaunchKernelGGL(( computeExpSum), dim3(block_count), dim3(thread_count), 0, stream_nx_exp, 
                exp_nxy,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                exp_nxy_sum
                );
       hipLaunchKernelGGL(( computeExpSum), dim3(block_count), dim3(thread_count), 0, stream_y_exp, 
                exp_xy+num_pins,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                exp_xy_sum+num_nets
                );
       hipLaunchKernelGGL(( computeExpSum), dim3(block_count), dim3(thread_count), 0, stream_ny_exp, 
                exp_nxy+num_pins,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                exp_nxy_sum+num_nets
                );

        // compute x exp sum
       hipLaunchKernelGGL(( computeXExpSum), dim3(block_count), dim3(thread_count), 0, stream_x_exp, 
                x,
                exp_xy,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xyexp_xy_sum
                );
       hipLaunchKernelGGL(( computeXExpSum), dim3(block_count), dim3(thread_count), 0, stream_nx_exp, 
                x,
                exp_nxy,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xyexp_nxy_sum
                );
       hipLaunchKernelGGL(( computeXExpSum), dim3(block_count), dim3(thread_count), 0, stream_y_exp, 
                y,
                exp_xy+num_pins,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xyexp_xy_sum+num_nets
                );
       hipLaunchKernelGGL(( computeXExpSum), dim3(block_count), dim3(thread_count), 0, stream_ny_exp, 
                y,
                exp_nxy+num_pins,
                pin2net_map,
                net_mask,
                num_nets,
                num_pins,
                xyexp_nxy_sum+num_nets
                );

        // compute log sum exp
       hipLaunchKernelGGL(( computeXExpSumByExpSum), dim3(block_count), dim3(thread_count), 0, stream_x_exp, 
                xyexp_xy_sum,
                exp_xy_sum,
                pin2net_map,
                net_mask,
                num_nets,
                gamma,
                partial_wl
                );
       hipLaunchKernelGGL(( computeXNegExpSumByNegExpSum), dim3(block_count), dim3(thread_count), 0, stream_nx_exp, 
                xyexp_nxy_sum,
                exp_nxy_sum,
                pin2net_map,
                net_mask,
                num_nets,
                gamma,
                partial_wl+num_nets
                );

       hipLaunchKernelGGL(( computeXExpSumByExpSum), dim3(block_count), dim3(thread_count), 0, stream_y_exp, 
                xyexp_xy_sum+num_nets,
                exp_xy_sum+num_nets,
                pin2net_map,
                net_mask,
                num_nets,
                gamma,
                partial_wl+2*num_nets
                );
       hipLaunchKernelGGL(( computeXNegExpSumByNegExpSum), dim3(block_count), dim3(thread_count), 0, stream_ny_exp, 
                xyexp_nxy_sum+num_nets,
                exp_nxy_sum+num_nets,
                pin2net_map,
                net_mask,
                num_nets,
                gamma,
                partial_wl+3*num_nets
                );

        // I move out the summation to use ATen
        // significant speedup is observed
        //sumArray<<<1, 1>>>(partial_wl, 2*num_nets, wl);

        status = hipStreamDestroy(stream_nx_exp);
        stream_nx_exp = 0;
        if (status != hipSuccess)
        {
            printf("stream_nx_exp destroy failed\n");
            fflush(stdout);
            return 1;
        }
        status = hipStreamDestroy(stream_ny_exp);
        stream_ny_exp = 0;
        if (status != hipSuccess)
        {
            printf("stream_ny_exp destroy failed\n");
            fflush(stdout);
            return 1;
        }
    }

    /* destroy stream */
    status = hipStreamDestroy(stream_x_exp);
    stream_x_exp = 0;
    if (status != hipSuccess)
    {
        printf("stream_x_exp destroy failed\n");
        fflush(stdout);
        return 1;
    }
    status = hipStreamDestroy(stream_y_exp);
    stream_y_exp = 0;
    if (status != hipSuccess)
    {
        printf("stream_y_exp destroy failed\n");
        fflush(stdout);
        return 1;
    }

    return 0;
}


#define REGISTER_KERNEL_LAUNCHER(T, V) \
    int instantiateComputeWeightedAverageWirelengthAtomicLauncher(\
            const T* x, const T* y, \
            const int* pin2net_map, \
            const unsigned char* net_mask, \
            int num_nets, \
            int num_pins, \
            const T* gamma, \
            T* exp_xy, T* exp_nxy, \
            T* exp_xy_sum, T* exp_nxy_sum,\
            T* xyexp_xy_sum, T* xyexp_nxy_sum, \
            V* xy_max, V* xy_min, \
            T* partial_wl, \
            const T* grad_tensor, \
            T* grad_x_tensor, T* grad_y_tensor \
            )\
    {\
        return computeWeightedAverageWirelengthHipAtomicLauncher(\
                x, y, \
                pin2net_map, \
                net_mask, \
                num_nets,\
                num_pins,\
                gamma, \
                exp_xy, exp_nxy, \
                exp_xy_sum, exp_nxy_sum, \
                xyexp_xy_sum, xyexp_nxy_sum, \
                xy_max, xy_min, \
                partial_wl, \
                grad_tensor, \
                grad_x_tensor, grad_y_tensor  \
                );\
    }
REGISTER_KERNEL_LAUNCHER(float, int);
REGISTER_KERNEL_LAUNCHER(double, int);

DREAMPLACE_END_NAMESPACE

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for density_overflow.

# Include the progress variables for this target.
include dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/progress.make

dreamplace/ops/density_overflow/CMakeFiles/density_overflow: dreamplace/ops/density_overflow/density_overflow.stamp


dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow.cpp
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_by_node.cpp
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_by_node_kernel.hip
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_by_node_kernel_hip.hip
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_kernel.hip
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_kernel_hip.hip
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_thread_map.cpp
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_thread_map_kernel.hip
dreamplace/ops/density_overflow/density_overflow.stamp: ../dreamplace/ops/density_overflow/src/density_overflow_hip_thread_map_kernel_hip.hip
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating density_overflow.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow/density_overflow.stamp

density_overflow: dreamplace/ops/density_overflow/CMakeFiles/density_overflow
density_overflow: dreamplace/ops/density_overflow/density_overflow.stamp
density_overflow: dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/build.make

.PHONY : density_overflow

# Rule to build all files generated by this target.
dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/build: density_overflow

.PHONY : dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/build

dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow && $(CMAKE_COMMAND) -P CMakeFiles/density_overflow.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/clean

dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/density_overflow /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/density_overflow/CMakeFiles/density_overflow.dir/depend


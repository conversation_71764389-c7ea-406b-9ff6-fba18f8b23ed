# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for density_potential.

# Include the progress variables for this target.
include dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/progress.make

dreamplace/ops/density_potential/CMakeFiles/density_potential: dreamplace/ops/density_potential/density_potential.stamp


dreamplace/ops/density_potential/density_potential.stamp: ../dreamplace/ops/density_potential/src/density_overflow_hip_kernel.hip
dreamplace/ops/density_potential/density_potential.stamp: ../dreamplace/ops/density_potential/src/density_overflow_hip_kernel_hip.hip
dreamplace/ops/density_potential/density_potential.stamp: ../dreamplace/ops/density_potential/src/density_potential.cpp
dreamplace/ops/density_potential/density_potential.stamp: ../dreamplace/ops/density_potential/src/density_potential_hip.cpp
dreamplace/ops/density_potential/density_potential.stamp: ../dreamplace/ops/density_potential/src/density_potential_hip_kernel.hip
dreamplace/ops/density_potential/density_potential.stamp: ../dreamplace/ops/density_potential/src/density_potential_hip_kernel_hip.hip
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating density_potential.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential/density_potential.stamp

density_potential: dreamplace/ops/density_potential/CMakeFiles/density_potential
density_potential: dreamplace/ops/density_potential/density_potential.stamp
density_potential: dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/build.make

.PHONY : density_potential

# Rule to build all files generated by this target.
dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/build: density_potential

.PHONY : dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/build

dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential && $(CMAKE_COMMAND) -P CMakeFiles/density_potential.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/clean

dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/density_potential /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/density_potential/CMakeFiles/density_potential.dir/depend


#!/usr/bin/env python3
##
# @file   test_ddp_setup.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Test script for DDP setup and communication functions
#

import os
import sys
import torch
import torch.multiprocessing as mp
import numpy as np

# Add dreamplace to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dreamplace'))

from dreamplace.ddp_shared_param_utils import (
    setup_ddp, cleanup_ddp, get_ddp_info, 
    all_reduce_tensor_sum, broadcast_tensor, 
    is_ddp_available, synchronize
)

def test_ddp_worker(rank, world_size):
    """
    @brief Test DDP functionality on a single worker
    @param rank current process rank
    @param world_size total number of processes
    """
    try:
        print(f"[Rank {rank}] Starting DDP test worker")
        
        # Setup DDP
        setup_ddp(rank, world_size)
        
        # Verify DDP info
        ddp_rank, ddp_world_size, is_init = get_ddp_info()
        print(f"[Rank {rank}] DDP info: rank={ddp_rank}, world_size={ddp_world_size}, initialized={is_init}")
        
        # Test device setup
        if torch.cuda.is_available():
            device = torch.device(f"cuda:{rank}")
            print(f"[Rank {rank}] Using device: {device}")
        else:
            device = torch.device("cpu")
            print(f"[Rank {rank}] Using device: {device}")
        
        # Test all_reduce
        test_tensor = torch.tensor([rank + 1.0], dtype=torch.float32, device=device)
        print(f"[Rank {rank}] Before all_reduce: {test_tensor.item()}")
        
        reduced_tensor = all_reduce_tensor_sum(test_tensor.clone())
        expected_sum = sum(range(1, world_size + 1))  # 1+2+...+world_size
        print(f"[Rank {rank}] After all_reduce: {reduced_tensor.item()}, expected: {expected_sum}")
        
        # Test broadcast
        if rank == 0:
            broadcast_data = torch.tensor([42.0], dtype=torch.float32, device=device)
        else:
            broadcast_data = torch.tensor([0.0], dtype=torch.float32, device=device)
        
        print(f"[Rank {rank}] Before broadcast: {broadcast_data.item()}")
        broadcast_tensor(broadcast_data, src=0)
        print(f"[Rank {rank}] After broadcast: {broadcast_data.item()}")
        
        # Test synchronization
        print(f"[Rank {rank}] Testing synchronization...")
        synchronize()
        print(f"[Rank {rank}] Synchronization complete")
        
        # Test DDP availability check
        ddp_available = is_ddp_available()
        print(f"[Rank {rank}] DDP available: {ddp_available}")
        
        print(f"[Rank {rank}] All tests passed!")
        
    except Exception as e:
        print(f"[Rank {rank}] Error: {e}")
        raise e
    finally:
        # Cleanup DDP
        cleanup_ddp()
        print(f"[Rank {rank}] DDP cleanup complete")

def test_single_gpu():
    """Test DDP functions in single GPU mode"""
    print("[I] Testing single GPU mode...")
    
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    
    # Test all_reduce (should be no-op)
    test_tensor = torch.tensor([5.0], dtype=torch.float32, device=device)
    print(f"Before all_reduce: {test_tensor.item()}")
    reduced_tensor = all_reduce_tensor_sum(test_tensor.clone())
    print(f"After all_reduce: {reduced_tensor.item()}")
    
    # Test broadcast (should be no-op)
    broadcast_data = torch.tensor([42.0], dtype=torch.float32, device=device)
    print(f"Before broadcast: {broadcast_data.item()}")
    broadcast_tensor(broadcast_data, src=0)
    print(f"After broadcast: {broadcast_data.item()}")
    
    # Test DDP availability
    ddp_available = is_ddp_available()
    print(f"DDP available: {ddp_available}")
    
    print("[I] Single GPU tests passed!")

def test_multi_gpu():
    """Test DDP functions in multi-GPU mode"""
    if not torch.cuda.is_available():
        print("[W] CUDA not available, skipping multi-GPU test")
        return
    
    world_size = torch.cuda.device_count()
    if world_size < 2:
        print("[W] Less than 2 GPUs available, skipping multi-GPU test")
        return
    
    print(f"[I] Testing multi-GPU mode with {world_size} GPUs...")
    
    try:
        # Launch multi-process DDP test
        mp.spawn(test_ddp_worker, args=(world_size,), nprocs=world_size, join=True)
        print("[I] Multi-GPU tests passed!")
    except Exception as e:
        print(f"[E] Multi-GPU test failed: {e}")
        raise e

def main():
    """Main test function"""
    print("=" * 60)
    print("Testing DDP Setup and Communication Functions")
    print("=" * 60)
    
    # Test single GPU mode
    test_single_gpu()
    print()
    
    # Test multi-GPU mode
    test_multi_gpu()
    print()
    
    print("=" * 60)
    print("All DDP tests completed!")
    print("=" * 60)

if __name__ == '__main__':
    main()

import sys
import os
sys.path.insert(0, 'source')

try:
    from dreamplace.ddp_shared_param_utils import broadcast_tensor_with_timeout
    print("✅ Import successful")
    
    import torch
    test_tensor = torch.tensor([42.0])
    result = broadcast_tensor_with_timeout(test_tensor, src=0, timeout_seconds=10)
    print(f"✅ Function call successful: {result.item()}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

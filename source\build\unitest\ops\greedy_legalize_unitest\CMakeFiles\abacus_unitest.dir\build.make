# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Include any dependencies generated for this target.
include unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/depend.make

# Include the progress variables for this target.
include unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/progress.make

# Include the compile flags for this target's objects.
include unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/flags.make

unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.o: unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/flags.make
unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.o: ../unitest/ops/greedy_legalize_unitest/abacus_unitest.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.o"
	cd /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.o -c /public/home/<USER>/DREAMPlace/source/unitest/ops/greedy_legalize_unitest/abacus_unitest.cpp

unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.i"
	cd /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /public/home/<USER>/DREAMPlace/source/unitest/ops/greedy_legalize_unitest/abacus_unitest.cpp > CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.i

unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.s"
	cd /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /public/home/<USER>/DREAMPlace/source/unitest/ops/greedy_legalize_unitest/abacus_unitest.cpp -o CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.s

# Object files for target abacus_unitest
abacus_unitest_OBJECTS = \
"CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.o"

# External object files for target abacus_unitest
abacus_unitest_EXTERNAL_OBJECTS =

unitest/ops/greedy_legalize_unitest/abacus_unitest: unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/abacus_unitest.cpp.o
unitest/ops/greedy_legalize_unitest/abacus_unitest: unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/build.make
unitest/ops/greedy_legalize_unitest/abacus_unitest: dreamplace/ops/utility/libutility.a
unitest/ops/greedy_legalize_unitest/abacus_unitest: unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable abacus_unitest"
	cd /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/abacus_unitest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/build: unitest/ops/greedy_legalize_unitest/abacus_unitest

.PHONY : unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/build

unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest && $(CMAKE_COMMAND) -P CMakeFiles/abacus_unitest.dir/cmake_clean.cmake
.PHONY : unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/clean

unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/unitest/ops/greedy_legalize_unitest /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest /public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : unitest/ops/greedy_legalize_unitest/CMakeFiles/abacus_unitest.dir/depend


##
# @file   PlaceObj_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware placement model class
#

import os
import sys
import time
import numpy as np
import itertools
import torch
import torch.distributed as dist
import torch.autograd as autograd
import torch.nn as nn
import torch.nn.functional as F
import pdb
import gzip
if sys.version_info[0] < 3:
    import cPickle as pickle
else:
    import _pickle as pickle

# Import shared parameter DDP-aware modules
import dreamplace.ops.weighted_average_wirelength.weighted_average_wirelength_shared_ddp as weighted_average_wirelength_shared_ddp
import dreamplace.ops.logsumexp_wirelength.logsumexp_wirelength as logsumexp_wirelength
import dreamplace.ops.electric_potential.electric_potential_shared_ddp as electric_potential_shared_ddp
import dreamplace.ops.density_potential.density_potential as density_potential

class PlaceObjSharedDDP(nn.Module):
    """
    @brief Shared parameter DDP-aware placement objective:
        wirelength + density_weight * density penalty
    Node positions are shared parameters across all GPUs
    """
    def __init__(self, density_weight, params, placedb, data_collections, op_collections, 
                 global_place_params, ddp_rank=0, ddp_world_size=1):
        """
        @brief initialize ops for shared parameter DDP placement
        @param ddp_rank current GPU rank
        @param ddp_world_size total number of GPUs
        """
        super(PlaceObjSharedDDP, self).__init__()

        self.gpu = params.gpu
        self.ddp_rank = ddp_rank
        self.ddp_world_size = ddp_world_size
        self.data_collections = data_collections
        self.op_collections = op_collections
        
        self.density_weight = torch.tensor([density_weight], 
                                         dtype=self.data_collections.pos[0].dtype, 
                                         device=self.data_collections.pos[0].device)
        self.gamma = torch.tensor(10*self.base_gamma(params, placedb), 
                                dtype=self.data_collections.pos[0].dtype, 
                                device=self.data_collections.pos[0].device)

        # Build shared parameter DDP-aware wirelength computation
        name = "%dx%d bins" % (global_place_params["num_bins_x"], global_place_params["num_bins_y"])
        if global_place_params["wirelength"] == "weighted_average":
            self.op_collections.wirelength_op, self.op_collections.update_gamma_op = \
                self.build_weighted_average_wl_shared_ddp(params, placedb, self.data_collections, 
                                                        self.op_collections.pin_pos_op)
        elif global_place_params["wirelength"] == "logsumexp":
            # LogSumExp not yet implemented for shared DDP, fallback to original
            self.op_collections.wirelength_op, self.op_collections.update_gamma_op = \
                self.build_logsumexp_wl(params, placedb, self.data_collections, 
                                      self.op_collections.pin_pos_op)
        else:
            assert 0, "unknown wirelength model %s" % (global_place_params["wirelength"])
            
        # Build shared parameter DDP-aware density computation
        self.op_collections.density_op = self.build_electric_potential_shared_ddp(
            params, placedb, self.data_collections,
            global_place_params["num_bins_x"], global_place_params["num_bins_y"],
            padding=0, name=name)
            
        # Other ops remain the same
        self.op_collections.update_density_weight_op = self.build_update_density_weight(params, placedb)
        self.op_collections.precondition_op = self.build_precondition(params, placedb, self.data_collections)
        self.op_collections.noise_op = self.build_noise(params, placedb, self.data_collections)

        self.iteration = global_place_params["iteration"]
        self.learning_rate = global_place_params["learning_rate"]

    def obj_fn(self, pos):
        """
        @brief Compute objective with shared parameter DDP support
        @param pos shared positions of cells
        @return objective value
        """
        wirelength = self.op_collections.wirelength_op(pos)
        if self.gpu:
            torch.cuda.synchronize()
            
        density = self.op_collections.density_op(pos)
        if self.gpu:
            torch.cuda.synchronize()
            
        return wirelength + self.density_weight * density

    def obj_and_grad_fn(self, pos):
        """
        @brief compute objective and gradient with shared parameter DDP support
        @param pos shared positions of cells
        @return objective value
        """
        obj = self.obj_fn(pos)

        if pos.grad is not None:
            pos.grad.zero_()
        if self.gpu:
            torch.cuda.synchronize()

        obj.backward()
        
        # Apply preconditioning
        self.op_collections.precondition_op(pos.grad)
        
        # For shared parameter DDP, gradients are already accumulated in the backward pass
        # No need for additional all_reduce here since our custom functions handle it
        
        return obj, pos.grad

    def forward(self):
        """
        @brief Compute objective with current locations of cells
        """
        return self.obj_fn(self.data_collections.pos[0])

    def build_weighted_average_wl_shared_ddp(self, params, placedb, data_collections, pin_pos_op):
        """
        @brief build shared parameter DDP-aware weighted average wirelength
        """
        # Create pin position operation
        pin_pos_operation = weighted_average_wirelength_shared_ddp.PinPosOp(
            data_collections.pin2node_map,
            data_collections.pin_offset_x,
            data_collections.pin_offset_y
        )

        # Create shared parameter DDP wirelength operator
        wirelength_for_pin_op = weighted_average_wirelength_shared_ddp.WeightedAverageWirelengthSharedDDP(
            flat_netpin=data_collections.flat_net2pin_map,
            netpin_start=data_collections.flat_net2pin_start_map,
            pin2net_map=data_collections.pin2net_map,
            net_mask=data_collections.net_mask_ignore_large_degrees,
            pin_mask=data_collections.pin_mask_ignore_fixed_macros,
            gamma=self.gamma,
            pin_pos_op=pin_pos_operation,
            algorithm='atomic',  # Use atomic for better performance
            num_threads=params.num_threads,
            ddp_rank=self.ddp_rank,
            ddp_world_size=self.ddp_world_size
        )

        # wirelength for position (directly use shared positions)
        def build_wirelength_op(pos):
            return wirelength_for_pin_op(pos)

        # update gamma
        base_gamma = self.base_gamma(params, placedb)
        def build_update_gamma_op(iteration, overflow):
            self.update_gamma(iteration, overflow, base_gamma)

        return build_wirelength_op, build_update_gamma_op

    def build_logsumexp_wl(self, params, placedb, data_collections, pin_pos_op):
        """
        @brief build log-sum-exp wirelength (fallback to original for now)
        TODO: Implement shared parameter DDP version
        """
        gamma = 10*self.base_gamma(params, placedb)
        print("[I] gamma = %g" % (gamma))

        wirelength_for_pin_op = logsumexp_wirelength.LogSumExpWirelength(
            flat_netpin=data_collections.flat_net2pin_map,
            netpin_start=data_collections.flat_net2pin_start_map,
            pin2net_map=data_collections.pin2net_map,
            net_mask=data_collections.net_mask_ignore_large_degrees,
            gamma=torch.tensor(gamma, dtype=data_collections.pos[0].dtype, device=data_collections.pos[0].device),
            algorithm='atomic',
            num_threads=params.num_threads
        )

        def build_wirelength_op(pos):
            pin_pos = pin_pos_op(pos)
            return wirelength_for_pin_op(pin_pos)

        base_gamma = self.base_gamma(params, placedb)
        def build_update_gamma_op(iteration, overflow):
            self.update_gamma(iteration, overflow, base_gamma)

        return build_wirelength_op, build_update_gamma_op

    def build_electric_potential_shared_ddp(self, params, placedb, data_collections, 
                                          num_bins_x, num_bins_y, padding, name):
        """
        @brief build shared parameter DDP-aware electric potential
        """
        bin_size_x = (placedb.xh-placedb.xl) / num_bins_x
        bin_size_y = (placedb.yh-placedb.yl) / num_bins_y

        xl = placedb.xl - padding*bin_size_x
        xh = placedb.xh + padding*bin_size_x
        yl = placedb.yl - padding*bin_size_y
        yh = placedb.yh + padding*bin_size_y
        local_num_bins_x = num_bins_x + 2*padding
        local_num_bins_y = num_bins_y + 2*padding
        
        print("[I] %s #bins %dx%d, bin sizes %gx%g, padding = %d" % 
              (name, local_num_bins_x, local_num_bins_y, 
               bin_size_x/placedb.row_height, bin_size_y/placedb.row_height, padding))

        return electric_potential_shared_ddp.ElectricPotentialSharedDDP(
            node_size_x=data_collections.node_size_x, 
            node_size_y=data_collections.node_size_y,
            bin_center_x=data_collections.bin_center_x_padded(placedb, padding), 
            bin_center_y=data_collections.bin_center_y_padded(placedb, padding),
            target_density=params.target_density,
            xl=xl, yl=yl, xh=xh, yh=yh,
            bin_size_x=bin_size_x, bin_size_y=bin_size_y,
            num_movable_nodes=placedb.num_movable_nodes,
            num_terminals=placedb.num_terminals,
            num_filler_nodes=placedb.num_filler_nodes,
            local_node_mask=data_collections.local_node_mask,  # Pass local node mask
            padding=padding,
            fast_mode=True,
            num_threads=params.num_threads,
            ddp_rank=self.ddp_rank,
            ddp_world_size=self.ddp_world_size
        )

    def initialize_density_weight(self, params, placedb):
        """
        @brief compute initial density weight for shared parameter DDP
        """
        print(f"[DEBUG] Rank {self.ddp_rank}: Starting initialize_density_weight")

        print(f"[DEBUG] Rank {self.ddp_rank}: Computing wirelength...")
        wirelength = self.op_collections.wirelength_op(self.data_collections.pos[0])
        print(f"[DEBUG] Rank {self.ddp_rank}: Wirelength computed: {wirelength.item()}")

        if self.data_collections.pos[0].grad is not None:
            self.data_collections.pos[0].grad.zero_()

        # 使用统一的错误处理确保所有GPU保持同步
        backward_success = True

        try:
            print(f"[DEBUG] Rank {self.ddp_rank}: Starting wirelength.backward()...")
            # 执行反向传播
            wirelength.backward()
            print(f"[DEBUG] Rank {self.ddp_rank}: wirelength.backward() completed")

        except RuntimeError as e:
            print(f"[E] Rank {self.ddp_rank}: Error in wirelength.backward(): {e}")
            backward_success = False

            # 清理梯度以避免不一致状态
            if self.data_collections.pos[0].grad is not None:
                self.data_collections.pos[0].grad.zero_()

        # 同步所有GPU的执行状态（使用安全的all_reduce）
        if self.ddp_world_size > 1 and dist.is_initialized():
            from dreamplace.ddp_shared_param_utils import all_reduce_tensor_sum

            # 创建状态张量进行同步
            status_tensor = torch.tensor([1.0 if backward_success else 0.0],
                                       device=self.data_collections.pos[0].device)

            # 使用MIN操作同步状态（如果任何GPU失败，结果为0）
            try:
                # 先转换为SUM操作兼容的形式
                world_size = dist.get_world_size()
                # 将状态转换：成功=world_size，失败=0
                status_tensor = status_tensor * world_size
                all_reduce_tensor_sum(status_tensor)
                # 检查是否所有GPU都成功（总和应该等于world_size^2）
                all_success = abs(status_tensor.item() - world_size * world_size) < 0.5
            except Exception as comm_e:
                print(f"[E] Rank {self.ddp_rank}: Communication error: {comm_e}")
                all_success = False

            # 如果有任何GPU失败，所有GPU都需要处理
            if not all_success:
                print(f"[W] Rank {self.ddp_rank}: Some GPU failed backward, using fallback")

                # 所有GPU都执行相同的fallback逻辑
                if self.data_collections.pos[0].grad is not None:
                    self.data_collections.pos[0].grad.zero_()

                # 简化的梯度计算（所有GPU执行相同逻辑）
                try:
                    with torch.no_grad():
                        # 使用零梯度作为安全fallback
                        self.data_collections.pos[0].grad = torch.zeros_like(self.data_collections.pos[0])
                except Exception as fallback_e:
                    print(f"[E] Rank {self.ddp_rank}: Fallback failed: {fallback_e}")
                    # 最后的安全措施
                    if self.data_collections.pos[0].grad is None:
                        self.data_collections.pos[0].grad = torch.zeros_like(self.data_collections.pos[0])

        # 安全计算wirelength梯度范数
        wirelength_grad_norm = self.data_collections.pos[0].grad.norm(p=1)
        if wirelength_grad_norm < 1e-10:
            print(f"[W] Rank {self.ddp_rank}: Wirelength grad norm too small ({wirelength_grad_norm}), using small value")
            wirelength_grad_norm = torch.tensor(1e-6, device=self.data_collections.pos[0].device)

        self.data_collections.pos[0].grad.zero_()

        # Density computation with unified error handling
        density_success = True

        try:
            print(f"[DEBUG] Rank {self.ddp_rank}: Starting density computation...")
            density = self.op_collections.density_op(self.data_collections.pos[0])
            print(f"[DEBUG] Rank {self.ddp_rank}: Density computed: {density.item()}")

            print(f"[DEBUG] Rank {self.ddp_rank}: Starting density.backward()...")
            density.backward()
            print(f"[DEBUG] Rank {self.ddp_rank}: density.backward() completed")
        except RuntimeError as e:
            print(f"[E] Rank {self.ddp_rank}: Error in density.backward(): {e}")
            density_success = False

        # 同步density计算状态
        if self.ddp_world_size > 1 and dist.is_initialized():
            from dreamplace.ddp_shared_param_utils import all_reduce_tensor_sum

            # 同步density状态
            density_status_tensor = torch.tensor([1.0 if density_success else 0.0],
                                               device=self.data_collections.pos[0].device)

            try:
                world_size = dist.get_world_size()
                density_status_tensor = density_status_tensor * world_size
                all_reduce_tensor_sum(density_status_tensor)
                all_density_success = abs(density_status_tensor.item() - world_size * world_size) < 0.5
            except Exception as comm_e:
                print(f"[E] Rank {self.ddp_rank}: Density communication error: {comm_e}")
                all_density_success = False

            if not all_density_success:
                print(f"[W] Rank {self.ddp_rank}: Some GPU failed density, using fallback")
                # 所有GPU使用相同的fallback - 使用小的随机梯度而不是零
                if self.data_collections.pos[0].grad is not None:
                    # 使用小的随机梯度避免除零
                    self.data_collections.pos[0].grad.normal_(0, 1e-6)

        density_grad_norm = self.data_collections.pos[0].grad.norm(p=1)

        # 防止除零的安全计算
        if density_grad_norm < 1e-10:
            print(f"[W] Rank {self.ddp_rank}: Density grad norm too small ({density_grad_norm}), using default ratio")
            grad_norm_ratio = 1.0  # 使用默认比例
        else:
            grad_norm_ratio = wirelength_grad_norm / density_grad_norm

        # 限制比例范围避免数值问题
        grad_norm_ratio = torch.clamp(grad_norm_ratio, min=1e-6, max=1e6)

        self.density_weight = torch.tensor([params.density_weight*grad_norm_ratio],
                                         dtype=self.data_collections.pos[0].dtype,
                                         device=self.data_collections.pos[0].device)

        return self.density_weight

    def build_update_density_weight(self, params, placedb):
        """
        @brief update density weight (same as original)
        """
        ref_hpwl = params.RePlAce_ref_hpwl
        LOWER_PCOF = params.RePlAce_LOWER_PCOF
        UPPER_PCOF = params.RePlAce_UPPER_PCOF
        def update_density_weight_op(metrics):
            with torch.no_grad():
                delta_hpwl = metrics[-1].hpwl-metrics[-2].hpwl
                if delta_hpwl < 0:
                    mu = UPPER_PCOF*np.maximum(np.power(0.9999, float(len(metrics))), 0.98)
                else:
                    mu = UPPER_PCOF*torch.pow(UPPER_PCOF, -delta_hpwl/ref_hpwl).clamp(min=LOWER_PCOF, max=UPPER_PCOF)
                self.density_weight *= mu

        return update_density_weight_op

    def base_gamma(self, params, placedb):
        """
        @brief compute base gamma (same as original)
        """
        return 4*(placedb.bin_size_x+placedb.bin_size_y)

    def update_gamma(self, iteration, overflow, base_gamma):
        """
        @brief update gamma in wirelength model (same as original)
        """
        coef = torch.pow(10, (overflow-0.1)*20/9-1)
        self.gamma.data.fill_(base_gamma*coef)
        return True

    def build_noise(self, params, placedb, data_collections):
        """
        @brief add noise to cell locations (adapted for shared parameter DDP)
        """
        node_size = torch.cat([data_collections.node_size_x, data_collections.node_size_y], dim=0).to(data_collections.pos[0].device)
        def noise_op(pos, noise_ratio):
            with torch.no_grad():
                noise = torch.rand_like(pos)
                noise.sub_(0.5).mul_(node_size).mul_(noise_ratio)
                # For shared parameter DDP, apply noise to all nodes but only on rank 0
                # to ensure consistency across GPUs
                if self.ddp_rank != 0:
                    noise.zero_()
                # No noise to fixed cells
                noise[placedb.num_movable_nodes:placedb.num_nodes-placedb.num_filler_nodes].zero_()
                noise[placedb.num_nodes+placedb.num_movable_nodes:2*placedb.num_nodes-placedb.num_filler_nodes].zero_()

                # Broadcast noise from rank 0 to all other ranks
                if self.ddp_world_size > 1 and dist.is_initialized():
                    dist.broadcast(noise, src=0)

                return pos.add_(noise)

        return noise_op

    def build_precondition(self, params, placedb, data_collections):
        """
        @brief preconditioning to gradient (adapted for shared parameter DDP)
        """
        num_pins_in_nodes = np.zeros(placedb.num_nodes)
        for i in range(placedb.num_physical_nodes):
            if i < len(placedb.node2pin_map):
                num_pins_in_nodes[i] = len(placedb.node2pin_map[i])

        num_pins_in_nodes = torch.tensor(num_pins_in_nodes, dtype=data_collections.pos[0].dtype, device=data_collections.pos[0].device)
        node_areas = data_collections.node_size_x * data_collections.node_size_y

        def precondition_op(grad):
            precond = num_pins_in_nodes + self.density_weight * node_areas
            precond.clamp_(min=1.0)
            grad[0:placedb.num_nodes].div_(precond)
            grad[placedb.num_nodes:placedb.num_nodes*2].div_(precond)
            return grad

        return precondition_op

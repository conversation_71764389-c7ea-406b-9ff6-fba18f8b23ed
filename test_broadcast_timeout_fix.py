#!/usr/bin/env python3
##
# @file   test_broadcast_timeout_fix.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Test script to verify broadcast timeout fixes
#

import os
import sys
import torch
import torch.multiprocessing as mp
import time

# Add dreamplace to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dreamplace'))

from dreamplace.ddp_shared_param_utils import (
    setup_ddp, cleanup_ddp, broadcast_tensor_with_timeout,
    apply_performance_optimizations
)

def test_broadcast_timeout(rank, world_size):
    """Test broadcast with timeout handling"""
    print(f"[Rank {rank}] Starting broadcast timeout test...")
    
    try:
        # Apply performance optimizations first
        apply_performance_optimizations()
        
        # Setup DDP
        setup_ddp(rank, world_size)
        
        device = torch.device(f'cuda:{rank}' if torch.cuda.is_available() else 'cpu')
        
        # Test 1: Normal broadcast (should work)
        print(f"[Rank {rank}] Test 1: Normal broadcast")
        test_tensor = torch.tensor([42.0 if rank == 0 else 0.0], device=device)
        print(f"[Rank {rank}] Before broadcast: {test_tensor.item()}")
        
        broadcast_tensor_with_timeout(test_tensor, src=0, timeout_seconds=30)
        print(f"[Rank {rank}] After broadcast: {test_tensor.item()}")
        
        # Test 2: Simulate density_weight and gamma broadcast
        print(f"[Rank {rank}] Test 2: Simulating density_weight broadcast")
        density_weight = torch.tensor([1.5 if rank == 0 else 0.0], device=device)
        gamma = torch.tensor([0.8 if rank == 0 else 0.0], device=device)
        
        print(f"[Rank {rank}] Before: density_weight={density_weight.item()}, gamma={gamma.item()}")
        
        # Add barrier before broadcast (like in the fix)
        torch.distributed.barrier()
        
        broadcast_tensor_with_timeout(density_weight, src=0, timeout_seconds=300)
        broadcast_tensor_with_timeout(gamma, src=0, timeout_seconds=300)
        
        print(f"[Rank {rank}] After: density_weight={density_weight.item()}, gamma={gamma.item()}")
        
        # Test 3: Stop decision broadcast
        print(f"[Rank {rank}] Test 3: Stop decision broadcast")
        should_stop = rank == 0  # Only rank 0 decides to stop
        stop_tensor = torch.tensor([should_stop], dtype=torch.bool, device=device)
        
        print(f"[Rank {rank}] Before stop broadcast: {stop_tensor.item()}")
        broadcast_tensor_with_timeout(stop_tensor, src=0, timeout_seconds=60)
        print(f"[Rank {rank}] After stop broadcast: {stop_tensor.item()}")
        
        print(f"[Rank {rank}] All broadcast tests passed!")
        
    except Exception as e:
        print(f"[Rank {rank}] Error: {e}")
        import traceback
        traceback.print_exc()
        raise e
    finally:
        # Cleanup DDP
        cleanup_ddp()
        print(f"[Rank {rank}] DDP cleanup completed")

def test_timeout_scenarios(rank, world_size):
    """Test various timeout scenarios"""
    print(f"[Rank {rank}] Testing timeout scenarios...")
    
    try:
        setup_ddp(rank, world_size)
        device = torch.device(f'cuda:{rank}' if torch.cuda.is_available() else 'cpu')
        
        # Test with very short timeout (should handle gracefully)
        print(f"[Rank {rank}] Testing short timeout...")
        test_tensor = torch.tensor([1.0], device=device)
        
        try:
            broadcast_tensor_with_timeout(test_tensor, src=0, timeout_seconds=1)
            print(f"[Rank {rank}] Short timeout test completed")
        except Exception as e:
            print(f"[Rank {rank}] Short timeout test failed as expected: {e}")
        
        print(f"[Rank {rank}] Timeout scenario tests completed")
        
    except Exception as e:
        print(f"[Rank {rank}] Timeout scenario error: {e}")
        raise e
    finally:
        cleanup_ddp()

def main():
    """Main test function"""
    world_size = 2  # Test with 2 processes
    
    if torch.cuda.is_available() and torch.cuda.device_count() >= world_size:
        print(f"Running broadcast timeout tests with {world_size} GPUs...")
        
        # Test 1: Normal broadcast functionality
        print("=== Test 1: Normal Broadcast Functionality ===")
        mp.spawn(test_broadcast_timeout, args=(world_size,), nprocs=world_size, join=True)
        
        # Test 2: Timeout scenarios
        print("=== Test 2: Timeout Scenarios ===")
        mp.spawn(test_timeout_scenarios, args=(world_size,), nprocs=world_size, join=True)
        
        print("All broadcast timeout tests completed successfully!")
        
    else:
        print(f"Insufficient GPUs available. Need {world_size}, have {torch.cuda.device_count()}")
        print("Running single-process test...")
        
        # Single process test
        test_broadcast_timeout(0, 1)
        test_timeout_scenarios(0, 1)

if __name__ == "__main__":
    main()

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for dct.

# Include the progress variables for this target.
include dreamplace/ops/dct/CMakeFiles/dct.dir/progress.make

dreamplace/ops/dct/CMakeFiles/dct: dreamplace/ops/dct/dct.stamp


dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_2N.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_2N_hip.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_hip.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_hip_kernel.hip
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_hip_kernel_hip.hip
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_lee.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_lee_hip.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_lee_hip_kernel.hip
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dct_lee_hip_kernel_hip.hip
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dst.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dst_hip.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dst_hip_kernel.hip
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dst_hip_kernel_hip.hip
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dxt.cpp
dreamplace/ops/dct/dct.stamp: ../dreamplace/ops/dct/src/dxt_hip.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating dct.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct/dct.stamp

dct: dreamplace/ops/dct/CMakeFiles/dct
dct: dreamplace/ops/dct/dct.stamp
dct: dreamplace/ops/dct/CMakeFiles/dct.dir/build.make

.PHONY : dct

# Rule to build all files generated by this target.
dreamplace/ops/dct/CMakeFiles/dct.dir/build: dct

.PHONY : dreamplace/ops/dct/CMakeFiles/dct.dir/build

dreamplace/ops/dct/CMakeFiles/dct.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct && $(CMAKE_COMMAND) -P CMakeFiles/dct.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/dct/CMakeFiles/dct.dir/clean

dreamplace/ops/dct/CMakeFiles/dct.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/dct /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct/CMakeFiles/dct.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/dct/CMakeFiles/dct.dir/depend


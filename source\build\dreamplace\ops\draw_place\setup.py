##
# @file   setup.py.in
# <AUTHOR>
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension, CUDAExtension

import os
import sys
import copy
import sysconfig
limbo_dir = "/public/home/<USER>/DREAMPlace/source/build/thirdparty/Limbo"
ops_dir = "/public/home/<USER>/DREAMPlace/source/dreamplace/ops"

include_dirs = [os.path.join(os.path.abspath(limbo_dir), 'include'), ops_dir, '/opt/myboost/boost_1_62_0/include', '/usr/include']
lib_dirs = [os.path.join(os.path.abspath(limbo_dir), 'lib'), '/opt/myboost/boost_1_62_0/lib', os.path.dirname('/usr/lib/x86_64-linux-gnu/libz.so'), '/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility']
libs = ['gdsparser', 'boost_iostreams', 'z', 'utility']

if "TRUE".upper() == 'TRUE':
    print("found Cairo and enable")
    include_dirs.append('/usr/include/cairo')
    lib_dirs.append(os.path.dirname('/usr/lib/x86_64-linux-gnu/libcairo.so'))
    libs.append('cairo')
    cairo_compile_args = '-DDRAWPLACE=1'
else:
    print("not found Cairo and disable")
    cairo_compile_args = '-DDRAWPLACE=0'

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

def add_prefix(filename):
    return os.path.join('/public/home/<USER>/DREAMPlace/source/dreamplace/ops/draw_place/src', filename)

setup(
        name='draw_place',
        ext_modules=[
            CppExtension('draw_place_cpp',
                [
                    add_prefix('draw_place.cpp'),
                    ],
                include_dirs=copy.deepcopy(include_dirs),
                library_dirs=copy.deepcopy(lib_dirs),
                libraries=copy.deepcopy(libs),
                extra_compile_args={
                    'cxx': ['-fvisibility=hidden', cairo_compile_args, torch_major_version, torch_minor_version],
                    },
                runtime_library_dirs=[python_lib] if python_lib else []
                ),
            ],
        cmdclass={
            'build_ext': BuildExtension
            })

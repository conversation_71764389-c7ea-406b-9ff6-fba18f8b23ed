##
# @file   ddp_shared_param_utils.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP utilities for DreamPlace
#

import torch
import torch.distributed as dist
import numpy as np
import os
import sys
import datetime
from typing import Tuple, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

def setup_ddp(rank: int, world_size: int, backend: str = 'nccl', master_addr: str = 'localhost', master_port: str = '12355'):
    """
    @brief Setup distributed data parallel training
    @param rank current process rank
    @param world_size total number of processes
    @param backend communication backend ('nccl' for GPU, 'gloo' for CPU)
    @param master_addr master node address
    @param master_port master node port
    """
    # Set environment variables for distributed training
    os.environ['MASTER_ADDR'] = master_addr
    os.environ['MASTER_PORT'] = master_port
    os.environ['RANK'] = str(rank)
    os.environ['WORLD_SIZE'] = str(world_size)

    # Determine backend based on device availability
    if backend == 'nccl' and not torch.cuda.is_available():
        backend = 'gloo'
        logger.warning(f"CUDA not available, switching to gloo backend")

    # Initialize the process group with timeout
    try:
        # Set a reasonable timeout for initialization (10 minutes)
        timeout = datetime.timedelta(minutes=10)

        dist.init_process_group(
            backend=backend,
            rank=rank,
            world_size=world_size,
            timeout=timeout
        )

        if rank == 0:
            logger.info(f"DDP initialized with backend={backend}, world_size={world_size}")

        # Set CUDA device for current rank if using NCCL
        if backend == 'nccl' and torch.cuda.is_available():
            torch.cuda.set_device(rank)

    except Exception as e:
        logger.error(f"Failed to initialize DDP: {e}")
        raise e

def cleanup_ddp():
    """
    @brief Cleanup distributed data parallel training
    """
    if dist.is_initialized():
        try:
            dist.destroy_process_group()
            logger.info("DDP process group destroyed")
        except Exception as e:
            logger.warning(f"Error during DDP cleanup: {e}")

def get_ddp_info():
    """
    @brief Get current DDP information
    @return tuple of (rank, world_size, is_initialized)
    """
    if dist.is_initialized():
        return dist.get_rank(), dist.get_world_size(), True
    else:
        return 0, 1, False

class SharedParamDDPPartitioner:
    """
    @brief Shared parameter DDP partitioner for DreamPlace
    Node positions are shared parameters across all GPUs
    Data is partitioned by nets for WA computation and by nodes for density computation
    """
    
    def __init__(self, rank: int, world_size: int):
        """
        @brief Initialize shared parameter DDP partitioner
        @param rank current GPU rank
        @param world_size total number of GPUs
        """
        self.rank = rank
        self.world_size = world_size
        
    def partition_nets_for_wa(self, placedb) -> Dict:
        """
        @brief Partition nets for weighted average wirelength computation
        Each GPU processes a subset of nets but keeps full pin data
        @param placedb placement database
        @return dictionary containing partitioned net data for current GPU
        """
        num_nets = len(placedb.net2pin_map)
        start_net, end_net = self._partition_range(num_nets, self.rank, self.world_size)

        logger.info(f"GPU {self.rank}: Processing nets {start_net} to {end_net-1} out of {num_nets}")

        # Create local net mask for this GPU's nets
        local_net_mask = np.zeros(num_nets, dtype=np.uint8)
        local_net_mask[start_net:end_net] = 1

        # Apply degree filtering
        net_degrees = np.array([len(pins) for pins in placedb.net2pin_map])
        ignore_net_degree = getattr(placedb, 'ignore_net_degree', 100)
        degree_mask = np.logical_and(2 <= net_degrees, net_degrees < ignore_net_degree).astype(np.uint8)

        # Combine local assignment and degree filtering
        final_net_mask = np.logical_and(local_net_mask, degree_mask).astype(np.uint8)

        return {
            'net_mask': final_net_mask,
            'start_net': start_net,
            'end_net': end_net,
            'num_local_nets': np.sum(final_net_mask),
            'local_net_ids': np.where(final_net_mask)[0]
        }
    
    def partition_nodes_for_density(self, placedb) -> Dict:
        """
        @brief Partition nodes for density computation with deduplication
        Each node's bin contribution is handled by only one GPU
        @param placedb placement database
        @return dictionary containing partitioned node data for current GPU
        """
        num_movable_nodes = placedb.num_movable_nodes
        start_node, end_node = self._partition_range(num_movable_nodes, self.rank, self.world_size)

        logger.info(f"GPU {self.rank}: Processing movable nodes {start_node} to {end_node-1} out of {num_movable_nodes}")

        # Create node assignment mask
        node_assignment = np.zeros(placedb.num_nodes, dtype=np.int32)

        # Assign movable nodes to GPUs
        for node_id in range(num_movable_nodes):
            assigned_gpu = self._get_node_assigned_gpu(node_id, num_movable_nodes, self.world_size)
            node_assignment[node_id] = assigned_gpu

        # Fixed nodes and fillers are assigned to GPU 0 for simplicity
        node_assignment[num_movable_nodes:] = 0

        # Create local node mask for this GPU
        local_node_mask = (node_assignment == self.rank).astype(np.uint8)

        return {
            'node_assignment': node_assignment,
            'local_node_mask': local_node_mask,
            'start_node': start_node,
            'end_node': end_node,
            'num_local_movable_nodes': end_node - start_node,
            'local_node_ids': np.where(local_node_mask)[0]
        }
    
    def _partition_range(self, total_size: int, rank: int, world_size: int) -> Tuple[int, int]:
        """
        @brief Calculate partition range for given rank
        @param total_size total number of items to partition
        @param rank current rank
        @param world_size total number of ranks
        @return (start_idx, end_idx) for current rank
        """
        items_per_rank = total_size // world_size
        remainder = total_size % world_size
        
        if rank < remainder:
            start_idx = rank * (items_per_rank + 1)
            end_idx = start_idx + items_per_rank + 1
        else:
            start_idx = remainder * (items_per_rank + 1) + (rank - remainder) * items_per_rank
            end_idx = start_idx + items_per_rank
            
        return start_idx, end_idx
    
    def _get_node_assigned_gpu(self, node_id: int, num_movable_nodes: int, world_size: int) -> int:
        """
        @brief Get which GPU a node is assigned to for density computation
        @param node_id node identifier
        @param num_movable_nodes total number of movable nodes
        @param world_size total number of GPUs
        @return assigned GPU rank
        """
        items_per_rank = num_movable_nodes // world_size
        remainder = num_movable_nodes % world_size
        
        if node_id < remainder * (items_per_rank + 1):
            return node_id // (items_per_rank + 1)
        else:
            return remainder + (node_id - remainder * (items_per_rank + 1)) // items_per_rank

class SharedParamDDPDataCollection:
    """
    @brief Shared parameter DDP data collection
    Manages shared node positions and partitioned computation data
    """
    
    def __init__(self, pos, params, placedb, device, partitioner: SharedParamDDPPartitioner):
        """
        @brief Initialize shared parameter DDP data collection
        @param pos shared position parameters
        @param params placement parameters
        @param placedb placement database
        @param device target device
        @param partitioner DDP data partitioner
        """
        # Apply performance optimizations
        apply_performance_optimizations()

        self.partitioner = partitioner
        self.device = device
        self.pos = pos  # Shared parameters

        # Partition data for current GPU
        self.net_partition = partitioner.partition_nets_for_wa(placedb)
        self.node_partition = partitioner.partition_nodes_for_density(placedb)
        
        # Setup shared data (same across all GPUs)
        self._setup_shared_data(placedb, device)
        
        # Setup partitioned data
        self._setup_partitioned_data(placedb, device)
        
    def _setup_shared_data(self, placedb, device):
        """Setup shared data that's identical across all GPUs with memory optimization"""
        # Get target dtype from pos tensor
        target_dtype = self.pos[0].dtype

        # Pin data (shared across all GPUs) - use efficient tensor creation
        self.pin_offset_x = torch.from_numpy(placedb.pin_offset_x).to(device=device, dtype=target_dtype, non_blocking=True)
        self.pin_offset_y = torch.from_numpy(placedb.pin_offset_y).to(device=device, dtype=target_dtype, non_blocking=True)

        # Mapping tensors - use int64 for indexing, non_blocking transfer
        self.pin2node_map = torch.from_numpy(placedb.pin2node_map).to(device=device, dtype=torch.int64, non_blocking=True)
        self.pin2net_map = torch.from_numpy(placedb.pin2net_map).to(device=device, dtype=torch.int64, non_blocking=True)

        # Node to pin mapping (shared across all GPUs)
        self.flat_node2pin_map = torch.from_numpy(placedb.flat_node2pin_map).to(device=device, dtype=torch.int64, non_blocking=True)
        self.flat_node2pin_start_map = torch.from_numpy(placedb.flat_node2pin_start_map).to(device=device, dtype=torch.int64, non_blocking=True)

        # Net data (shared across all GPUs)
        self.flat_net2pin_map = torch.from_numpy(placedb.flat_net2pin_map).to(device=device, dtype=torch.int64, non_blocking=True)
        self.flat_net2pin_start_map = torch.from_numpy(placedb.flat_net2pin_start_map).to(device=device, dtype=torch.int64, non_blocking=True)

        # Node data (shared across all GPUs)
        self.node_size_x = torch.from_numpy(placedb.node_size_x).to(device=device, dtype=target_dtype, non_blocking=True)
        self.node_size_y = torch.from_numpy(placedb.node_size_y).to(device=device, dtype=target_dtype, non_blocking=True)

        # Bin data (shared across all GPUs)
        self.bin_center_x = torch.from_numpy(placedb.bin_center_x).to(device=device, dtype=target_dtype, non_blocking=True)
        self.bin_center_y = torch.from_numpy(placedb.bin_center_y).to(device=device, dtype=target_dtype, non_blocking=True)

        # Synchronize to ensure all transfers complete before proceeding
        if device.type == 'cuda':
            torch.cuda.synchronize(device)
        
    def _setup_partitioned_data(self, placedb, device):
        """Setup partitioned data specific to current GPU"""
        # Net masks
        # All nets mask (shared across all GPUs, used for HPWL evaluation)
        self.net_mask_all = torch.from_numpy(np.ones(len(placedb.net2pin_map), dtype=np.uint8)).to(device)

        # Net mask for WA computation (only this GPU's nets)
        self.net_mask_ignore_large_degrees = torch.from_numpy(self.net_partition['net_mask']).to(device)

        # Node mask for density computation (only this GPU's nodes)
        self.local_node_mask = torch.from_numpy(self.node_partition['local_node_mask']).to(device)

        # Pin mask for fixed macros (shared logic)
        self.pin_mask_ignore_fixed_macros = (self.pin2node_map >= placedb.num_movable_nodes)
        
    def get_pin_pos(self):
        """
        @brief Compute pin positions from node positions (shared across all GPUs)
        This maintains the original pos -> pin_pos transformation
        @return pin positions tensor
        """
        num_nodes = self.pos[0].shape[0] // 2
        num_pins = self.pin2node_map.shape[0]
        
        # Extract node positions
        node_x = self.pos[0][:num_nodes]
        node_y = self.pos[0][num_nodes:]
        
        # Compute pin positions
        pin_x = node_x[self.pin2node_map] + self.pin_offset_x
        pin_y = node_y[self.pin2node_map] + self.pin_offset_y
        
        # Combine pin positions
        pin_pos = torch.cat([pin_x, pin_y])
        
        return pin_pos
    
    def bin_center_x_padded(self, placedb, padding):
        """Compute padded bin centers (same as original implementation)"""
        if padding == 0:
            return self.bin_center_x
        else:
            bin_size_x = (placedb.xh - placedb.xl) / placedb.num_bins_x
            xl = placedb.xl - padding * bin_size_x
            xh = placedb.xh + padding * bin_size_x
            bin_center_x_padded = torch.from_numpy(placedb.bin_centers(xl, xh, bin_size_x)).to(self.device)
            return bin_center_x_padded
            
    def bin_center_y_padded(self, placedb, padding):
        """Compute padded bin centers (same as original implementation)"""
        if padding == 0:
            return self.bin_center_y
        else:
            bin_size_y = (placedb.yh - placedb.yl) / placedb.num_bins_y
            yl = placedb.yl - padding * bin_size_y
            yh = placedb.yh + padding * bin_size_y
            bin_center_y_padded = torch.from_numpy(placedb.bin_centers(yl, yh, bin_size_y)).to(self.device)
            return bin_center_y_padded

def all_reduce_gradients_sum(tensor: torch.Tensor) -> None:
    """
    @brief All-reduce gradients with SUM operation (no averaging)
    @param tensor tensor to reduce
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.all_reduce(tensor, op=dist.ReduceOp.SUM)
        except Exception as e:
            logger.error(f"Failed to all_reduce gradients: {e}")
            raise e

def all_reduce_tensor_sum(tensor: torch.Tensor) -> torch.Tensor:
    """
    @brief All-reduce tensor with SUM operation (with built-in deadlock prevention)
    @param tensor tensor to reduce (modified in-place)
    @return reduced tensor
    """
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return tensor

    try:
        # Ensure tensor is contiguous for optimal communication
        if not tensor.is_contiguous():
            tensor = tensor.contiguous()

        # Simple all-reduce with error handling
        dist.all_reduce(tensor, op=dist.ReduceOp.SUM)

    except Exception as e:
        rank = dist.get_rank() if dist.is_initialized() else 0
        logger.error(f"Rank {rank}: All-reduce failed: {e}")
        # For communication failures, continue with local tensor to avoid deadlock
        logger.warning(f"Rank {rank}: Continuing with local tensor value")

    return tensor

def broadcast_tensor(tensor: torch.Tensor, src: int = 0) -> torch.Tensor:
    """
    @brief Broadcast tensor from source rank to all ranks
    @param tensor tensor to broadcast
    @param src source rank
    @return broadcasted tensor
    """
    if dist.is_initialized() and dist.get_world_size() > 1:
        try:
            dist.broadcast(tensor, src=src)
        except Exception as e:
            logger.error(f"Failed to broadcast tensor: {e}")
            raise e
    return tensor

def broadcast_tensor_with_timeout(tensor: torch.Tensor, src: int = 0, timeout_seconds: int = 300) -> torch.Tensor:
    """
    @brief Broadcast tensor with timeout handling and deadlock prevention
    @param tensor tensor to broadcast
    @param src source rank
    @param timeout_seconds timeout in seconds (default: 5 minutes)
    @return broadcasted tensor
    """
    if not dist.is_initialized() or dist.get_world_size() <= 1:
        return tensor

    import signal
    import threading
    import time

    rank = dist.get_rank()

    # Ensure tensor is contiguous and on correct device
    if not tensor.is_contiguous():
        tensor = tensor.contiguous()

    # Use a timeout mechanism
    broadcast_completed = threading.Event()
    broadcast_exception = None

    def broadcast_worker():
        nonlocal broadcast_exception
        try:
            # Add a small delay to ensure all ranks are ready
            time.sleep(0.1)
            dist.broadcast(tensor, src=src)
            broadcast_completed.set()
        except Exception as e:
            broadcast_exception = e
            broadcast_completed.set()

    # Start broadcast in a separate thread
    broadcast_thread = threading.Thread(target=broadcast_worker)
    broadcast_thread.daemon = True
    broadcast_thread.start()

    # Wait for completion with timeout
    if broadcast_completed.wait(timeout=timeout_seconds):
        if broadcast_exception:
            logger.error(f"Rank {rank}: Broadcast failed: {broadcast_exception}")
            raise broadcast_exception
        logger.debug(f"Rank {rank}: Broadcast completed successfully")
    else:
        logger.error(f"Rank {rank}: Broadcast timeout after {timeout_seconds} seconds")
        # Try to interrupt the broadcast thread (best effort)
        try:
            # Force thread termination is not safe, but we log the timeout
            logger.warning(f"Rank {rank}: Broadcast operation timed out, continuing with local tensor")
        except:
            pass
        # Don't raise exception to avoid deadlock - let the caller handle it

    return tensor

def is_ddp_available() -> bool:
    """
    @brief Check if DDP is available and properly initialized
    @return True if DDP is available, False otherwise
    """
    return dist.is_available() and dist.is_initialized() and dist.get_world_size() > 1

def synchronize():
    """
    @brief Synchronize all processes in DDP with built-in error handling
    """
    if not is_ddp_available():
        return

    try:
        dist.barrier()
    except Exception as e:
        rank = dist.get_rank() if dist.is_initialized() else 0
        logger.error(f"Rank {rank}: Synchronization failed: {e}")
        # For sync failures, log but don't raise to avoid cascading failures
        logger.warning(f"Rank {rank}: Continuing without synchronization")

def apply_performance_optimizations():
    """
    @brief Apply built-in performance optimizations and deadlock prevention
    """
    import os
    import gc

    # Memory optimizations
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # Compute optimizations
    if torch.cuda.is_available():
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cuda.matmul.allow_tf32 = True

    # Set optimal number of threads
    num_cores = os.cpu_count()
    if hasattr(torch, 'set_num_threads'):
        torch.set_num_threads(min(num_cores, 8))

    # DDP communication optimizations and deadlock prevention
    if torch.cuda.is_available():
        # Performance settings
        os.environ.setdefault('NCCL_BUFFSIZE', '8388608')  # 8MB
        os.environ.setdefault('NCCL_NTHREADS', '8')
        os.environ.setdefault('NCCL_NSOCKS_PERTHREAD', '4')

        # Deadlock prevention settings - more conservative timeouts
        os.environ.setdefault('NCCL_TIMEOUT', '1800')  # 30 minutes timeout (increased from 10)
        os.environ.setdefault('NCCL_BLOCKING_WAIT', '1')  # Better error reporting
        os.environ.setdefault('NCCL_ASYNC_ERROR_HANDLING', '1')  # Async error handling
        os.environ.setdefault('NCCL_DEBUG', 'WARN')  # Enable warnings

        # Additional stability settings
        os.environ.setdefault('NCCL_IB_DISABLE', '1')  # Disable InfiniBand if causing issues
        os.environ.setdefault('NCCL_P2P_DISABLE', '1')  # Disable P2P if causing issues
        os.environ.setdefault('NCCL_SHM_DISABLE', '1')  # Disable shared memory if causing issues

def get_memory_info():
    """
    @brief Get current memory usage information
    @return dictionary with memory information
    """
    try:
        import psutil
        info = {
            'cpu_memory_mb': psutil.virtual_memory().used / 1024**2,
            'cpu_memory_percent': psutil.virtual_memory().percent
        }
    except ImportError:
        info = {'cpu_memory_mb': 0, 'cpu_memory_percent': 0}

    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1024**2
            reserved = torch.cuda.memory_reserved(i) / 1024**2
            total = torch.cuda.get_device_properties(i).total_memory / 1024**2

            info[f'gpu_{i}_allocated_mb'] = allocated
            info[f'gpu_{i}_reserved_mb'] = reserved
            info[f'gpu_{i}_total_mb'] = total
            info[f'gpu_{i}_utilization'] = allocated / total * 100

    return info

def test_ddp_communication():
    """
    @brief Test DDP communication health
    @return True if communication is working, False otherwise
    """
    if not is_ddp_available():
        return True

    rank = dist.get_rank()
    world_size = dist.get_world_size()

    try:
        # Simple communication test
        test_tensor = torch.tensor([rank], dtype=torch.float32)
        if torch.cuda.is_available():
            test_tensor = test_tensor.cuda()

        # Test all_reduce
        all_reduce_tensor_sum(test_tensor)

        # Check result
        expected_sum = sum(range(world_size))
        if abs(test_tensor.item() - expected_sum) > 1e-6:
            logger.error(f"Rank {rank}: Communication test failed: expected {expected_sum}, got {test_tensor.item()}")
            return False

        logger.info(f"Rank {rank}: DDP communication test passed")
        return True

    except Exception as e:
        logger.error(f"Rank {rank}: DDP communication test failed: {e}")
        return False

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Include any dependencies generated for this target.
include thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/depend.make

# Include the progress variables for this target.
include thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/progress.make

# Include the compile flags for this target's objects.
include thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/flags.make

thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.o: thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/flags.make
thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.o: ../thirdparty/flute-3.1/rand-pts.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.o"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/rand-pts.dir/rand-pts.c.o   -c /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/rand-pts.c

thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/rand-pts.dir/rand-pts.c.i"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/rand-pts.c > CMakeFiles/rand-pts.dir/rand-pts.c.i

thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/rand-pts.dir/rand-pts.c.s"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1/rand-pts.c -o CMakeFiles/rand-pts.dir/rand-pts.c.s

# Object files for target rand-pts
rand__pts_OBJECTS = \
"CMakeFiles/rand-pts.dir/rand-pts.c.o"

# External object files for target rand-pts
rand__pts_EXTERNAL_OBJECTS =

thirdparty/flute-3.1/rand-pts: thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/rand-pts.c.o
thirdparty/flute-3.1/rand-pts: thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build.make
thirdparty/flute-3.1/rand-pts: thirdparty/flute-3.1/libflute.a
thirdparty/flute-3.1/rand-pts: thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable rand-pts"
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rand-pts.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build: thirdparty/flute-3.1/rand-pts

.PHONY : thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/build

thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 && $(CMAKE_COMMAND) -P CMakeFiles/rand-pts.dir/cmake_clean.cmake
.PHONY : thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/clean

thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1 /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1 /public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : thirdparty/flute-3.1/CMakeFiles/rand-pts.dir/depend


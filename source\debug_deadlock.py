#!/usr/bin/env python3
##
# @file   debug_deadlock.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Debug DCU deadlock issues
#

import torch
import torch.distributed as dist
import time
import os
import sys

def test_basic_operations():
    """Test basic tensor operations"""
    print("=== Testing Basic Operations ===")
    
    try:
        # Test tensor creation
        x = torch.randn(1000, device='cuda')
        print(f"✅ Tensor creation: {x.shape}")
        
        # Test basic math
        y = x * 2 + 1
        print(f"✅ Basic math: {y.mean().item():.4f}")
        
        # Test reduction
        z = x.sum()
        print(f"✅ Reduction: {z.item():.4f}")
        
        return True
    except Exception as e:
        print(f"❌ Basic operations failed: {e}")
        return False

def test_ddp_communication():
    """Test DDP communication step by step"""
    print("\n=== Testing DDP Communication ===")
    
    if not dist.is_initialized():
        print("❌ DDP not initialized")
        return False
    
    rank = dist.get_rank()
    world_size = dist.get_world_size()
    
    try:
        # Test 1: Simple tensor
        print(f"Rank {rank}: Testing simple tensor...")
        test_tensor = torch.tensor([rank], dtype=torch.float32, device='cuda')
        print(f"Rank {rank}: Before all_reduce: {test_tensor.item()}")
        
        dist.all_reduce(test_tensor, op=dist.ReduceOp.SUM)
        expected = sum(range(world_size))
        print(f"Rank {rank}: After all_reduce: {test_tensor.item()}, expected: {expected}")
        
        if abs(test_tensor.item() - expected) > 1e-6:
            print(f"❌ Rank {rank}: Communication test failed")
            return False
        
        print(f"✅ Rank {rank}: Simple communication works")
        
        # Test 2: Larger tensor
        print(f"Rank {rank}: Testing larger tensor...")
        large_tensor = torch.randn(10000, device='cuda')
        original_sum = large_tensor.sum().item()
        
        dist.all_reduce(large_tensor, op=dist.ReduceOp.SUM)
        print(f"✅ Rank {rank}: Large tensor communication works")
        
        # Test 3: Barrier
        print(f"Rank {rank}: Testing barrier...")
        dist.barrier()
        print(f"✅ Rank {rank}: Barrier works")
        
        return True
        
    except Exception as e:
        print(f"❌ Rank {rank}: DDP communication failed: {e}")
        return False

def test_dreamplace_kernels():
    """Test DreamPlace specific kernels"""
    print("\n=== Testing DreamPlace Kernels ===")
    
    try:
        # Test pin position computation
        print("Testing pin position computation...")
        num_nodes = 1000
        num_pins = 2000
        
        pos = torch.randn(num_nodes * 2, device='cuda')
        pin2node_map = torch.randint(0, num_nodes, (num_pins,), device='cuda', dtype=torch.int64)
        pin_offset_x = torch.randn(num_pins, device='cuda')
        pin_offset_y = torch.randn(num_pins, device='cuda')
        
        # This might be where it hangs
        node_x = pos[:num_nodes]
        node_y = pos[num_nodes:]
        
        print("Computing pin positions...")
        pin_x = node_x[pin2node_map] + pin_offset_x
        pin_y = node_y[pin2node_map] + pin_offset_y
        
        print(f"✅ Pin position computation: {pin_x.shape}, {pin_y.shape}")
        
        # Test weighted average wirelength (simplified)
        print("Testing simplified WA computation...")
        try:
            import dreamplace.ops.weighted_average_wirelength.weighted_average_wirelength_hip_atomic as wa_atomic
            
            # Create minimal test data
            pin_pos = torch.cat([pin_x, pin_y])
            pin2net_map = torch.randint(0, 100, (num_pins,), device='cuda', dtype=torch.int32)
            net_mask = torch.ones(100, device='cuda', dtype=torch.uint8)
            gamma = torch.tensor([1.0], device='cuda')
            
            print("Calling WA atomic kernel...")
            result = wa_atomic.forward(pin_pos, pin2net_map, net_mask, gamma)
            print(f"✅ WA atomic kernel works: {len(result)} outputs")
            
        except Exception as e:
            print(f"❌ WA atomic kernel failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ DreamPlace kernels failed: {e}")
        return False

def test_memory_access_patterns():
    """Test for memory access issues"""
    print("\n=== Testing Memory Access Patterns ===")
    
    try:
        # Test potential out-of-bounds access
        print("Testing index bounds...")
        
        num_nodes = 1000
        num_pins = 2000
        
        # Create valid indices
        pin2node_map = torch.randint(0, num_nodes, (num_pins,), device='cuda', dtype=torch.int64)
        
        # Check for out-of-bounds
        max_index = pin2node_map.max().item()
        min_index = pin2node_map.min().item()
        
        print(f"Index range: [{min_index}, {max_index}], num_nodes: {num_nodes}")
        
        if max_index >= num_nodes:
            print(f"❌ Out-of-bounds index detected: {max_index} >= {num_nodes}")
            return False
        
        if min_index < 0:
            print(f"❌ Negative index detected: {min_index}")
            return False
        
        print("✅ Index bounds check passed")
        
        # Test actual indexing
        pos = torch.randn(num_nodes * 2, device='cuda')
        node_x = pos[:num_nodes]
        
        print("Testing indexing operation...")
        indexed_values = node_x[pin2node_map]
        print(f"✅ Indexing works: {indexed_values.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory access test failed: {e}")
        return False

def monitor_gpu_activity():
    """Monitor GPU activity during operations"""
    print("\n=== Monitoring GPU Activity ===")
    
    try:
        import subprocess
        
        # Get initial GPU state
        result = subprocess.run(['rocm-smi'], capture_output=True, text=True, timeout=5)
        print("Initial GPU state:")
        print(result.stdout)
        
        # Test a potentially hanging operation
        print("Starting potentially hanging operation...")
        
        # Create large tensors
        x = torch.randn(10000, 10000, device='cuda')
        y = torch.randn(10000, 10000, device='cuda')
        
        print("Performing matrix multiplication...")
        start_time = time.time()
        z = torch.mm(x, y)
        end_time = time.time()
        
        print(f"✅ Matrix multiplication completed in {end_time - start_time:.2f}s")
        
        # Check GPU state again
        result = subprocess.run(['rocm-smi'], capture_output=True, text=True, timeout=5)
        print("Final GPU state:")
        print(result.stdout)
        
        return True
        
    except Exception as e:
        print(f"❌ GPU monitoring failed: {e}")
        return False

def main():
    """Main debugging function"""
    print("🔍 DreamPlace DCU Deadlock Debugger")
    print("=" * 50)
    
    rank = 0
    if dist.is_initialized():
        rank = dist.get_rank()
    
    print(f"Running on rank {rank}")
    
    # Test 1: Basic operations
    if not test_basic_operations():
        print("❌ Basic operations failed - GPU hardware issue?")
        return
    
    # Test 2: DDP communication (if applicable)
    if dist.is_initialized():
        if not test_ddp_communication():
            print("❌ DDP communication failed - network/NCCL issue?")
            return
    else:
        print("ℹ️ DDP not initialized, skipping communication tests")
    
    # Test 3: DreamPlace kernels
    if not test_dreamplace_kernels():
        print("❌ DreamPlace kernels failed - kernel implementation issue?")
        return
    
    # Test 4: Memory access patterns
    if not test_memory_access_patterns():
        print("❌ Memory access failed - indexing issue?")
        return
    
    # Test 5: GPU monitoring
    if not monitor_gpu_activity():
        print("❌ GPU monitoring failed")
        return
    
    print("\n✅ All tests passed! The deadlock might be in a specific combination of operations.")
    print("💡 Try running the actual training with more detailed logging.")

if __name__ == '__main__':
    main()

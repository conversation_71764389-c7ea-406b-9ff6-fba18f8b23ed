##
# @file   setup.py.in
# <AUTHOR> Li
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension, CUDAExtension

import os
import sys
import copy
import sysconfig
ops_dir = "${OPS_DIR}"
include_dirs = [ops_dir]
lib_dirs = ['${UTILITY_LIBRARY_DIRS}']
libs = ['utility']

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

def add_prefix(filename):
    return os.path.join('${CMAKE_CURRENT_SOURCE_DIR}/src', filename)

modules = []

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

modules.extend([
    CppExtension('greedy_legalize_cpp',
        [
            add_prefix('greedy_legalize.cpp'),
            add_prefix('legalize_bin_cpu.cpp'),
            add_prefix('bin_assignment_cpu.cpp'),
            add_prefix('merge_bin_cpu.cpp'),
            add_prefix('greedy_legalize_cpu.cpp')
            ],
        include_dirs=copy.deepcopy(include_dirs),
        library_dirs=copy.deepcopy(lib_dirs),
        libraries=copy.deepcopy(libs),
        extra_compile_args={
            #'cxx': ['-g'],
            'cxx': [torch_major_version, torch_minor_version],
            },
        runtime_library_dirs=[python_lib] if python_lib else []
        )
    ])

setup(
        name='greedy_legalize',
        ext_modules=modules,
        cmdclass={
            'build_ext': BuildExtension
            })

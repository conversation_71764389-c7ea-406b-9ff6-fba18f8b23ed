#!/usr/bin/env python3
##
# @file   run_shared_ddp_placement.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Launch script for shared parameter DDP placement training
#

import os
import sys
import argparse
import json
import subprocess
import torch

def create_default_config():
    """Create a default configuration for shared parameter DDP placement"""
    config = {
        "aux_file": "",
        "gpu": True,
        "num_threads": 8,
        "global_place_flag": True,
        "legalize_flag": True,
        "detailed_place_flag": False,
        "stop_overflow": 0.1,
        "density_weight": 8e-5,
        "target_density": 1.0,
        "bin_size_x": 2.0,
        "bin_size_y": 2.0,
        "random_seed": 1000,
        "scale_factor": 1.0,
        "ignore_net_degree": 100,
        "gp_noise_ratio": 0.025,
        "enable_fillers": True,
        "plot_flag": False,
        "detailed_place_engine": "",
        "result_dir": "results",
        "global_place_stages": [
            {
                "num_bins_x": 32,
                "num_bins_y": 32,
                "iteration": 1000,
                "learning_rate": 0.01,
                "wirelength": "weighted_average",
                "optimizer": "nesterov"
            },
            {
                "num_bins_x": 64,
                "num_bins_y": 64,
                "iteration": 1000,
                "learning_rate": 0.005,
                "wirelength": "weighted_average",
                "optimizer": "nesterov"
            },
            {
                "num_bins_x": 128,
                "num_bins_y": 128,
                "iteration": 1000,
                "learning_rate": 0.002,
                "wirelength": "weighted_average",
                "optimizer": "nesterov"
            }
        ],
        "RePlAce_ref_hpwl": 350000000,
        "RePlAce_LOWER_PCOF": 0.95,
        "RePlAce_UPPER_PCOF": 1.05
    }
    return config

def main():
    parser = argparse.ArgumentParser(description='Run shared parameter DDP placement training')
    parser.add_argument('--aux_file', type=str, required=True,
                        help='Path to .aux file')
    parser.add_argument('--config', type=str, default=None,
                        help='Path to configuration JSON file')
    parser.add_argument('--num_gpus', type=int, default=None,
                        help='Number of GPUs to use (default: all available)')
    parser.add_argument('--output_dir', type=str, default='shared_ddp_results',
                        help='Output directory for results')
    parser.add_argument('--plot', action='store_true',
                        help='Enable plotting')
    parser.add_argument('--detailed_place_engine', type=str, default='',
                        help='Path to detailed placement engine')
    
    args = parser.parse_args()
    
    # Check if aux file exists
    if not os.path.exists(args.aux_file):
        print(f"[E] AUX file not found: {args.aux_file}")
        sys.exit(1)
    
    # Check GPU availability
    if not torch.cuda.is_available():
        print("[W] CUDA not available, will use CPU")
        num_gpus = 0
    else:
        available_gpus = torch.cuda.device_count()
        num_gpus = args.num_gpus if args.num_gpus is not None else available_gpus
        num_gpus = min(num_gpus, available_gpus)
        print(f"[I] Using {num_gpus} out of {available_gpus} available GPUs")
    
    # Load or create configuration
    if args.config:
        if not os.path.exists(args.config):
            print(f"[E] Config file not found: {args.config}")
            sys.exit(1)
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = create_default_config()
    
    # Update configuration with command line arguments
    config['aux_file'] = os.path.abspath(args.aux_file)
    config['gpu'] = num_gpus > 0
    config['plot_flag'] = args.plot
    config['result_dir'] = args.output_dir
    if args.detailed_place_engine:
        config['detailed_place_engine'] = args.detailed_place_engine
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Save configuration
    config_file = os.path.join(args.output_dir, 'shared_ddp_config.json')
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    print(f"[I] Configuration saved to: {config_file}")
    
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    placer_script = os.path.join(script_dir, 'dreamplace', 'Placer_shared_ddp.py')
    
    if not os.path.exists(placer_script):
        print(f"[E] Placer script not found: {placer_script}")
        sys.exit(1)
    
    # Set environment variables for DDP
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = ','.join(str(i) for i in range(num_gpus)) if num_gpus > 0 else ''
    
    # Run shared parameter DDP placement
    cmd = [sys.executable, placer_script, config_file]
    print(f"[I] Running command: {' '.join(cmd)}")
    print(f"[I] Working directory: {script_dir}")
    print(f"[I] Environment: CUDA_VISIBLE_DEVICES={env.get('CUDA_VISIBLE_DEVICES', 'Not set')}")
    
    try:
        result = subprocess.run(cmd, cwd=script_dir, env=env, check=True)
        print("[I] Shared parameter DDP placement completed successfully")
    except subprocess.CalledProcessError as e:
        print(f"[E] Shared parameter DDP placement failed with return code {e.returncode}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("[I] Shared parameter DDP placement interrupted by user")
        sys.exit(1)

if __name__ == '__main__':
    main()

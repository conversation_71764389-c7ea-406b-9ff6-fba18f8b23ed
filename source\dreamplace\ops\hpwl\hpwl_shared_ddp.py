##
# @file   hpwl_shared_ddp.py
# <AUTHOR> Assistant
# @date   2024
# @brief  Shared parameter DDP-aware HPWL computation (备选方案)
#

import torch
import torch.distributed as dist
from torch import nn
from torch.autograd import Function

# Import original HPWL modules
import dreamplace.ops.hpwl.hpwl_cpp as hpwl_cpp
try:
    import dreamplace.ops.hpwl.hpwl_hip as hpwl_hip
    import dreamplace.ops.hpwl.hpwl_hip_atomic as hpwl_hip_atomic
except:
    pass

from dreamplace.ddp_shared_param_utils import all_reduce_tensor_sum

class HPWLSharedDDPFunction(Function):
    """
    @brief Shared parameter DDP-aware HPWL computation
    Each GPU computes HPWL for its assigned nets, then all-reduce for global HPWL
    """
    @staticmethod
    def forward(ctx, pos, flat_netpin, netpin_start, pin2net_map, net_mask, algorithm, num_threads):
        """
        @param pos pin positions (shared across all GPUs)
        @param net_mask local net mask for current GPU (only this GPU's nets are enabled)
        """
        # Compute local HPWL using original implementation
        if pos.is_cuda:
            if algorithm == 'atomic':
                # Convert pin2net_map to int32 for C++/HIP extension compatibility
                pin2net_map_int32 = pin2net_map.int() if pin2net_map.dtype != torch.int32 else pin2net_map
                local_hpwl = hpwl_hip_atomic.forward(pos.view(pos.numel()), pin2net_map_int32, net_mask)
            else:
                # Convert tensors to int32 for C++/HIP extension compatibility
                flat_netpin_int32 = flat_netpin.int() if flat_netpin.dtype != torch.int32 else flat_netpin
                netpin_start_int32 = netpin_start.int() if netpin_start.dtype != torch.int32 else netpin_start
                local_hpwl = hpwl_hip.forward(pos.view(pos.numel()), flat_netpin_int32, netpin_start_int32, net_mask)
        else:
            # Convert tensors to int32 for C++ extension compatibility
            flat_netpin_int32 = flat_netpin.int() if flat_netpin.dtype != torch.int32 else flat_netpin
            netpin_start_int32 = netpin_start.int() if netpin_start.dtype != torch.int32 else netpin_start
            local_hpwl = hpwl_cpp.forward(pos.view(pos.numel()), flat_netpin_int32, netpin_start_int32, net_mask, num_threads)
        
        # All-reduce to sum HPWL from all GPUs
        global_hpwl = all_reduce_tensor_sum(local_hpwl.clone())
        
        return global_hpwl

class HPWLSharedDDP(nn.Module):
    """
    @brief Shared parameter DDP-aware HPWL computation
    Each GPU computes HPWL for its assigned nets, results are summed across GPUs
    """
    def __init__(self, flat_netpin=None, netpin_start=None, pin2net_map=None, 
                 net_mask=None, algorithm='atomic', num_threads=8):
        """
        @brief initialization for shared parameter DDP HPWL
        @param net_mask local net mask for current GPU
        """
        super(HPWLSharedDDP, self).__init__()
        
        assert net_mask is not None, "net_mask is a required parameter"
        if algorithm == 'net-by-net':
            assert flat_netpin is not None and netpin_start is not None, \
                "flat_netpin, netpin_start are required for net-by-net algorithm"
        elif algorithm == 'atomic':
            assert pin2net_map is not None, \
                "pin2net_map is required for atomic algorithm"
        
        self.flat_netpin = flat_netpin
        self.netpin_start = netpin_start
        self.pin2net_map = pin2net_map
        self.net_mask = net_mask
        self.algorithm = algorithm
        self.num_threads = num_threads

    def forward(self, pos):
        """Forward pass for shared parameter DDP HPWL computation"""
        return HPWLSharedDDPFunction.apply(
            pos,
            self.flat_netpin,
            self.netpin_start,
            self.pin2net_map,
            self.net_mask,
            self.algorithm,
            self.num_threads
        )

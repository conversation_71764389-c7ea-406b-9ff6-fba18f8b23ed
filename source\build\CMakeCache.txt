# This is the CMakeCache file.
# For build in directory: /public/home/<USER>/DREAMPlace/source/build
# It was generated by CMake: /opt/cmake/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//The directory containing a CMake configuration file for AMDDeviceLibs.
AMDDeviceLibs_DIR:PATH=/opt/dtk/lib/cmake/AMDDeviceLibs

//DCU targets to compile for
AMDGPU_TARGETS:STRING=gfx906;gfx926

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=Boost_DIR-NOTFOUND

//Path to a file.
Boost_INCLUDE_DIR:PATH=/opt/myboost/boost_1_62_0/include

//Path to a file.
CAIRO_INCLUDE_DIRS:PATH=/usr/include/cairo

//Path to a library.
CAIRO_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libcairo.so

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: Debug Release.
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//Choose the value for _GLIBCXX_USE_CXX11_ABI, options are: 0|1.
CMAKE_CXX_ABI:STRING=1

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//Fortran compiler
CMAKE_Fortran_COMPILER:FILEPATH=/usr/bin/gfortran

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_Fortran_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_Fortran_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the Fortran compiler during all build types.
CMAKE_Fortran_FLAGS:STRING=

//Flags used by the Fortran compiler during DEBUG builds.
CMAKE_Fortran_FLAGS_DEBUG:STRING=-g

//Flags used by the Fortran compiler during MINSIZEREL builds.
CMAKE_Fortran_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG -Os

//Flags used by the Fortran compiler during RELEASE builds.
CMAKE_Fortran_FLAGS_RELEASE:STRING=-O3 -DNDEBUG -O3

//Flags used by the Fortran compiler during RELWITHDEBINFO builds.
CMAKE_Fortran_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/opt/software/HOCPlace

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=DREAMPlace

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Value Computed by CMake
DREAMPlace_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build

//Value Computed by CMake
DREAMPlace_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source

//Path to a file.
FLUTE_INCLUDE_DIRS:PATH=/public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1

//Path to a file.
FLUTE_SOURCE_DIR:PATH=/public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1

//GPU targets to compile for
GPU_TARGETS:STRING=gfx906;gfx926

//Path to a file.
HIP_CLANG_INCLUDE_PATH:PATH=/opt/dtk/llvm/lib/clang/14.0.0/include

//The directory containing a CMake configuration file for HIP.
HIP_DIR:PATH=/opt/dtk/lib/cmake/hip

//Path to a file.
HSA_HEADER:PATH=/opt/dtk-23.04/include

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a program.
PYTHON:FILEPATH=/usr/local/bin/python

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libz.so

//The directory containing a CMake configuration file for amd_comgr.
amd_comgr_DIR:PATH=/opt/dtk/lib/cmake/amd_comgr

//Value Computed by CMake
benchmarks_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/benchmarks

//Value Computed by CMake
benchmarks_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/benchmarks

//Value Computed by CMake
dct_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct

//Value Computed by CMake
dct_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/dct

//Value Computed by CMake
density_overflow_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow

//Value Computed by CMake
density_overflow_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/density_overflow

//Value Computed by CMake
density_potential_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential

//Value Computed by CMake
density_potential_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/density_potential

//Value Computed by CMake
draw_place_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place

//Value Computed by CMake
draw_place_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/draw_place

//Value Computed by CMake
electric_potential_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/electric_potential

//Value Computed by CMake
electric_potential_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/electric_potential

//Value Computed by CMake
flute_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/thirdparty/flute-3.1

//Value Computed by CMake
flute_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/thirdparty/flute-3.1

//Value Computed by CMake
greedy_legalize_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize

//Value Computed by CMake
greedy_legalize_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/greedy_legalize

//Value Computed by CMake
greedy_legalize_unitest_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/unitest/ops/greedy_legalize_unitest

//Value Computed by CMake
greedy_legalize_unitest_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/unitest/ops/greedy_legalize_unitest

//Value Computed by CMake
hpwl_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl

//Value Computed by CMake
hpwl_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/hpwl

//The directory containing a CMake configuration file for hsa-runtime64.
hsa-runtime64_DIR:PATH=/opt/dtk/lib/cmake/hsa-runtime64

//Value Computed by CMake
logsumexp_wirelength_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/logsumexp_wirelength

//Value Computed by CMake
logsumexp_wirelength_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/logsumexp_wirelength

//Value Computed by CMake
move_boundary_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/move_boundary

//Value Computed by CMake
move_boundary_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/move_boundary

//Path to a library.
pkgcfg_lib_PC_CAIRO_cairo:FILEPATH=/usr/lib/x86_64-linux-gnu/libcairo.so

//Value Computed by CMake
place_io_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/place_io

//Value Computed by CMake
place_io_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/place_io

//Value Computed by CMake
place_io_unitest_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/unitest/ops/place_io_unitest

//Value Computed by CMake
place_io_unitest_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/unitest/ops/place_io_unitest

//Value Computed by CMake
rmst_wl_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl

//Value Computed by CMake
rmst_wl_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/rmst_wl

//Value Computed by CMake
test_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/test

//Value Computed by CMake
test_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/test

//Value Computed by CMake
thirdparty_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/thirdparty

//Value Computed by CMake
thirdparty_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/thirdparty

//Value Computed by CMake
utility_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility

//Value Computed by CMake
utility_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/utility

//Value Computed by CMake
weighted_average_wirelength_BINARY_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength

//Value Computed by CMake
weighted_average_wirelength_SOURCE_DIR:STATIC=/public/home/<USER>/DREAMPlace/source/dreamplace/ops/weighted_average_wirelength


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/public/home/<USER>/DREAMPlace/source/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=16
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/cmake/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/cmake/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/cmake/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/cmake/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//ADVANCED property for variable: CMAKE_Fortran_COMPILER
CMAKE_Fortran_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_COMPILER_AR
CMAKE_Fortran_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_COMPILER_RANLIB
CMAKE_Fortran_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_FLAGS
CMAKE_Fortran_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_FLAGS_DEBUG
CMAKE_Fortran_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_FLAGS_MINSIZEREL
CMAKE_Fortran_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_FLAGS_RELEASE
CMAKE_Fortran_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_Fortran_FLAGS_RELWITHDEBINFO
CMAKE_Fortran_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/public/home/<USER>/DREAMPlace/source
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=24
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/cmake/share/cmake-3.16
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/opt/myboost/boost_1_62_0/include][c ][v1.62.0(1.60.0)]
//Details about finding Cairo
FIND_PACKAGE_MESSAGE_DETAILS_Cairo:INTERNAL=[/usr/include/cairo][/usr/lib/x86_64-linux-gnu/libcairo.so][TRUE][v()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v0.29.1()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/usr/lib/x86_64-linux-gnu/libz.so][/usr/include][v1.2.11()]
PC_CAIRO_CFLAGS:INTERNAL=-I/usr/include/cairo;-I/usr/include/glib-2.0;-I/usr/lib/x86_64-linux-gnu/glib-2.0/include;-I/usr/include/pixman-1;-I/usr/include/uuid;-I/usr/include/freetype2;-I/usr/include/libpng16
PC_CAIRO_CFLAGS_I:INTERNAL=
PC_CAIRO_CFLAGS_OTHER:INTERNAL=
PC_CAIRO_FOUND:INTERNAL=1
PC_CAIRO_INCLUDEDIR:INTERNAL=/usr/include
PC_CAIRO_INCLUDE_DIRS:INTERNAL=/usr/include/cairo;/usr/include/glib-2.0;/usr/lib/x86_64-linux-gnu/glib-2.0/include;/usr/include/pixman-1;/usr/include/uuid;/usr/include/freetype2;/usr/include/libpng16
PC_CAIRO_LDFLAGS:INTERNAL=-lcairo
PC_CAIRO_LDFLAGS_OTHER:INTERNAL=
PC_CAIRO_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_CAIRO_LIBRARIES:INTERNAL=cairo
PC_CAIRO_LIBRARY_DIRS:INTERNAL=
PC_CAIRO_LIBS:INTERNAL=
PC_CAIRO_LIBS_L:INTERNAL=
PC_CAIRO_LIBS_OTHER:INTERNAL=
PC_CAIRO_LIBS_PATHS:INTERNAL=
PC_CAIRO_MODULE_NAME:INTERNAL=cairo
PC_CAIRO_PREFIX:INTERNAL=/usr
PC_CAIRO_STATIC_CFLAGS:INTERNAL=-I/usr/include/cairo;-I/usr/include/glib-2.0;-I/usr/lib/x86_64-linux-gnu/glib-2.0/include;-I/usr/include/pixman-1;-I/usr/include/uuid;-I/usr/include/freetype2;-I/usr/include/libpng16
PC_CAIRO_STATIC_CFLAGS_I:INTERNAL=
PC_CAIRO_STATIC_CFLAGS_OTHER:INTERNAL=
PC_CAIRO_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/cairo;/usr/include/glib-2.0;/usr/lib/x86_64-linux-gnu/glib-2.0/include;/usr/include/pixman-1;/usr/include/uuid;/usr/include/freetype2;/usr/include/libpng16
PC_CAIRO_STATIC_LDFLAGS:INTERNAL=-lcairo;-lz;-lgobject-2.0;-pthread;-lffi;-lglib-2.0;-pthread;-lpcre;-pthread;-lpixman-1;-lfontconfig;-luuid;-lexpat;-lfreetype;-lpng16;-lm;-lz;-lm;-lz;-lxcb-shm;-lxcb-render;-lXrender;-lXext;-lX11;-lpthread;-lxcb;-lXau;-lXdmcp
PC_CAIRO_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
PC_CAIRO_STATIC_LIBDIR:INTERNAL=
PC_CAIRO_STATIC_LIBRARIES:INTERNAL=cairo;z;gobject-2.0;ffi;glib-2.0;pcre;pixman-1;fontconfig;uuid;expat;freetype;png16;m;z;m;z;xcb-shm;xcb-render;Xrender;Xext;X11;pthread;xcb;Xau;Xdmcp
PC_CAIRO_STATIC_LIBRARY_DIRS:INTERNAL=
PC_CAIRO_STATIC_LIBS:INTERNAL=
PC_CAIRO_STATIC_LIBS_L:INTERNAL=
PC_CAIRO_STATIC_LIBS_OTHER:INTERNAL=
PC_CAIRO_STATIC_LIBS_PATHS:INTERNAL=
PC_CAIRO_VERSION:INTERNAL=1.16.0
PC_CAIRO_cairo_INCLUDEDIR:INTERNAL=
PC_CAIRO_cairo_LIBDIR:INTERNAL=
PC_CAIRO_cairo_PREFIX:INTERNAL=
PC_CAIRO_cairo_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=/opt/myboost/boost_1_62_0/include
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=TRUE
__pkg_config_arguments_PC_CAIRO:INTERNAL=cairo
__pkg_config_checked_PC_CAIRO:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_CAIRO_cairo
pkgcfg_lib_PC_CAIRO_cairo-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu


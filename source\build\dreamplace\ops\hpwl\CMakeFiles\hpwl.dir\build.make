# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/cmake/bin/cmake

# The command to remove a file.
RM = /opt/cmake/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /public/home/<USER>/DREAMPlace/source

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /public/home/<USER>/DREAMPlace/source/build

# Utility rule file for hpwl.

# Include the progress variables for this target.
include dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/progress.make

dreamplace/ops/hpwl/CMakeFiles/hpwl: dreamplace/ops/hpwl/hpwl.stamp


dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl.cpp
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_atomic.cpp
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_hip.cpp
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_hip_atomic.cpp
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_hip_atomic_kernel.hip
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_hip_atomic_kernel_hip.hip
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_hip_kernel.hip
dreamplace/ops/hpwl/hpwl.stamp: ../dreamplace/ops/hpwl/src/hpwl_hip_kernel_hip.hip
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/public/home/<USER>/DREAMPlace/source/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating hpwl.stamp"
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl && /usr/local/bin/python /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl/setup.py build --build-temp=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl/build --build-lib=/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl/lib
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl && /opt/cmake/bin/cmake -E touch /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl/hpwl.stamp

hpwl: dreamplace/ops/hpwl/CMakeFiles/hpwl
hpwl: dreamplace/ops/hpwl/hpwl.stamp
hpwl: dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/build.make

.PHONY : hpwl

# Rule to build all files generated by this target.
dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/build: hpwl

.PHONY : dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/build

dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/clean:
	cd /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl && $(CMAKE_COMMAND) -P CMakeFiles/hpwl.dir/cmake_clean.cmake
.PHONY : dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/clean

dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/depend:
	cd /public/home/<USER>/DREAMPlace/source/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /public/home/<USER>/DREAMPlace/source /public/home/<USER>/DREAMPlace/source/dreamplace/ops/hpwl /public/home/<USER>/DREAMPlace/source/build /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl /public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : dreamplace/ops/hpwl/CMakeFiles/hpwl.dir/depend


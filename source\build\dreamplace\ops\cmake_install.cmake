# Install script for directory: /public/home/<USER>/DREAMPlace/source/dreamplace/ops

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/opt/software/HOCPlace")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/dreamplace/ops" TYPE FILE FILES "/public/home/<USER>/DREAMPlace/source/dreamplace/ops/__init__.py")
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/utility/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/dct/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_overflow/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/density_potential/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/logsumexp_wirelength/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/draw_place/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/electric_potential/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/greedy_legalize/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/hpwl/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/move_boundary/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/weighted_average_wirelength/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/rmst_wl/cmake_install.cmake")
  include("/public/home/<USER>/DREAMPlace/source/build/dreamplace/ops/place_io/cmake_install.cmake")

endif()


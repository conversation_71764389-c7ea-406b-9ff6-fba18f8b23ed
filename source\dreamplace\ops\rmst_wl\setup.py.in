##
# @file   setup.py.in
# <AUTHOR> Li
# @date   10 2024
# @brief  For CMake to generate setup.py file
#

import os
from setuptools import setup
import torch
from torch.utils.cpp_extension import BuildExtension, CppExtension, CUDAExtension
import copy
import sysconfig
utility_dir = "${UTILITY_LIBRARY_DIRS}"
ops_dir = '${OPS_DIR}'

tokens = str(torch.__version__).split('.')
torch_major_version = "-DTORCH_MAJOR_VERSION=%d" % (int(tokens[0]))
torch_minor_version = "-DTORCH_MINOR_VERSION=%d" % (int(tokens[1]))

def add_prefix(filename):
    return os.path.join('${CMAKE_CURRENT_SOURCE_DIR}/src', filename)

modules = []
lib_dirs = []
libs = []

python_lib = sysconfig.get_config_var('LIBDIR')
python_version = sysconfig.get_config_var('LDVERSION')
if python_lib and python_version:
    lib_dirs.append(python_lib)
    libs.append(f'python{python_version}')

modules.extend([
    CppExtension('rmst_wl_cpp',
        [
            add_prefix('rmst_wl.cpp')
            ],
        include_dirs=['${FLUTE_INCLUDE_DIRS}', ops_dir],
        library_dirs=['${FLUTE_LINK_DIRS}', utility_dir] + copy.deepcopy(lib_dirs),
        libraries=['flute', 'utility'] + copy.deepcopy(libs),
        extra_compile_args={
            'cxx' : [torch_major_version, torch_minor_version]
            },
        runtime_library_dirs=[python_lib] if python_lib else []
        ),
    ])

setup(
        name='rmst_wl',
        ext_modules=modules,
        cmdclass={
            'build_ext': BuildExtension
            })
